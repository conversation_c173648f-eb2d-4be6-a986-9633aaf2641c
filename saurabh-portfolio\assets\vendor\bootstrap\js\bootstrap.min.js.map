{"version": 3, "names": ["elementMap", "Map", "Data", "set", "element", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "keys", "remove", "delete", "TRANSITION_END", "parseSelector", "selector", "window", "CSS", "escape", "replace", "match", "id", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "object", "j<PERSON>y", "nodeType", "getElement", "length", "document", "querySelector", "isVisible", "getClientRects", "elementIsVisible", "getComputedStyle", "getPropertyValue", "closedDetails", "closest", "summary", "parentNode", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "getAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "push", "execute", "<PERSON><PERSON><PERSON><PERSON>", "args", "defaultValue", "call", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "split", "getTransitionDurationFromElement", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "listLength", "index", "indexOf", "Math", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "makeEventUid", "uid", "getElementEvents", "<PERSON><PERSON><PERSON><PERSON>", "events", "callable", "delegationSelector", "Object", "values", "find", "event", "normalizeParameters", "originalTypeEvent", "delegationFunction", "isDelegated", "typeEvent", "getTypeEvent", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFunction", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "this", "handlers", "previousFunction", "dom<PERSON><PERSON>s", "querySelectorAll", "dom<PERSON>lement", "hydrateObj", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "entries", "includes", "on", "one", "inNamespace", "isNamespace", "startsWith", "elementEvent", "slice", "keyHandlers", "trigger", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "preventDefault", "obj", "meta", "value", "_unused", "defineProperty", "configurable", "normalizeData", "toString", "JSON", "parse", "decodeURIComponent", "normalizeDataKey", "chr", "toLowerCase", "Manipulator", "setDataAttribute", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "bs<PERSON><PERSON>s", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "Config", "<PERSON><PERSON><PERSON>", "DefaultType", "Error", "_getConfig", "config", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "jsonConfig", "constructor", "configTypes", "property", "expectedTypes", "valueType", "prototype", "RegExp", "test", "TypeError", "toUpperCase", "BaseComponent", "super", "_element", "_config", "DATA_KEY", "dispose", "EVENT_KEY", "propertyName", "getOwnPropertyNames", "_queueCallback", "isAnimated", "getInstance", "getOrCreateInstance", "VERSION", "eventName", "getSelector", "hrefAttribute", "trim", "map", "sel", "join", "SelectorEngine", "concat", "Element", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "el", "getSelectorFromElement", "getElementFromSelector", "getMultipleElementsFromSelector", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "EVENT_CLOSE", "EVENT_CLOSED", "<PERSON><PERSON>", "close", "_destroyElement", "each", "data", "undefined", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "button", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "endCallback", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "Swipe", "isSupported", "_deltaX", "_supportPointerEvents", "PointerEvent", "_initEvents", "_start", "_eventIsPointerPenTouch", "clientX", "touches", "_end", "_handleSwipe", "_move", "absDeltaX", "abs", "direction", "add", "pointerType", "navigator", "maxTouchPoints", "DATA_API_KEY", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "EVENT_CLICK_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "SELECTOR_ACTIVE", "SELECTOR_ITEM", "SELECTOR_ACTIVE_ITEM", "KEY_TO_DIRECTION", "ARROW_LEFT_KEY$1", "ARROW_RIGHT_KEY$1", "interval", "keyboard", "pause", "ride", "touch", "wrap", "Carousel", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "_addEventListeners", "cycle", "_slide", "nextWhenVisible", "hidden", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "to", "items", "_getItems", "activeIndex", "_getItemIndex", "_getActive", "order", "defaultInterval", "_keydown", "_addTouchEventListeners", "img", "swipeConfig", "_directionToOrder", "endCallBack", "clearTimeout", "_setActiveIndicatorElement", "activeIndicator", "newActiveIndicator", "elementInterval", "parseInt", "isNext", "nextElement", "nextElementIndex", "triggerEvent", "_orderToDirection", "isCycling", "directionalClassName", "orderClassName", "completeCallBack", "_isAnimated", "clearInterval", "carousel", "slideIndex", "carousels", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_DEEPER_CHILDREN", "parent", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElement", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "activeInstance", "dimension", "_getDimension", "style", "scrollSize", "complete", "getBoundingClientRect", "selected", "trigger<PERSON><PERSON>y", "isOpen", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "autoClose", "boundary", "display", "offset", "popperConfig", "reference", "Dropdown", "_popper", "_parent", "_menu", "_inNavbar", "_detectNavbar", "_createPopper", "focus", "_completeHide", "destroy", "update", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "createPopper", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "placement", "modifiers", "options", "enabled", "_selectMenuItem", "clearMenus", "openToggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "dataApiKeydownHandler", "isInput", "isEscapeEvent", "isUpOrDownEvent", "getToggleButton", "stopPropagation", "EVENT_MOUSEDOWN", "className", "clickCallback", "rootElement", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "EVENT_FOCUSIN", "EVENT_KEYDOWN_TAB", "TAB_NAV_BACKWARD", "autofocus", "trapElement", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "elements", "shift<PERSON>ey", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "getWidth", "documentWidth", "clientWidth", "innerWidth", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "reset", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "overflow", "styleProperty", "scrollbarWidth", "_applyManipulationCallback", "setProperty", "actualValue", "removeProperty", "callBack", "EVENT_HIDE_PREVENTED", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "EVENT_KEYDOWN_DISMISS", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_scrollBar", "_adjustDialog", "_showElement", "_hideModal", "handleUpdate", "scrollTop", "modalBody", "transitionComplete", "_triggerBackdropTransition", "event2", "_resetAdjustments", "isModalOverflowing", "scrollHeight", "clientHeight", "initialOverflowY", "overflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "alreadyOpen", "CLASS_NAME_SHOWING", "CLASS_NAME_HIDING", "OPEN_SELECTOR", "scroll", "<PERSON><PERSON><PERSON>", "blur", "completeCallback", "position", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "dd", "div", "dl", "dt", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "i", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "uriAttributes", "SAFE_URL_PATTERN", "allowedAttribute", "attribute", "allowedAttributeList", "attributeName", "nodeName", "nodeValue", "attributeRegex", "some", "regex", "allowList", "content", "extraClass", "html", "sanitize", "sanitizeFn", "template", "DefaultContentType", "entry", "TemplateFactory", "get<PERSON>ontent", "_resolvePossibleFunction", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "_checkContent", "toHtml", "templateWrapper", "innerHTML", "_maybeSanitize", "text", "_setContent", "arg", "templateElement", "_putElementInTemplate", "textContent", "unsafeHtml", "sanitizeFunction", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "elementName", "attributeList", "allowedAttributes", "sanitizeHtml", "DISALLOWED_ATTRIBUTES", "CLASS_NAME_FADE", "SELECTOR_TOOLTIP_INNER", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "animation", "container", "customClass", "delay", "fallbackPlacements", "title", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_templateFactory", "_newContent", "tip", "_setListeners", "_fixTitle", "enable", "disable", "toggle<PERSON>nabled", "_leave", "_enter", "_hideModalHandler", "_disposePopper", "_isWithContent", "isInTheDom", "ownerDocument", "_getTipElement", "_isWithActiveTrigger", "_getTitle", "_createTipElement", "_getContentForTemplate", "_getTemplateFactory", "tipId", "prefix", "floor", "random", "getElementById", "getUID", "<PERSON><PERSON><PERSON><PERSON>", "_initializeOnDelegatedTarget", "_getDelegateConfig", "attachment", "phase", "state", "triggers", "eventIn", "eventOut", "_setTimeout", "timeout", "dataAttributes", "dataAttribute", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "EVENT_ACTIVATE", "EVENT_CLICK", "SELECTOR_TARGET_LINKS", "SELECTOR_NAV_LINKS", "SELECTOR_LINK_ITEMS", "rootMargin", "smoothScroll", "threshold", "ScrollSpy", "_targetLinks", "_observableSections", "_rootElement", "_activeTarget", "_observer", "_previousScrollData", "visibleEntryTop", "parentScrollTop", "refresh", "_initializeTargetsAndObservables", "_maybeEnableSmoothScroll", "disconnect", "_getNewObserver", "section", "observe", "observableSection", "hash", "height", "offsetTop", "scrollTo", "top", "behavior", "IntersectionObserver", "_<PERSON><PERSON><PERSON><PERSON>", "targetElement", "_process", "userScrollsDown", "isIntersecting", "_clearActiveClass", "entryIsLowerThanPrevious", "targetLinks", "anchor", "decodeURI", "_activateParents", "listGroup", "item", "activeNodes", "node", "spy", "HOME_KEY", "END_KEY", "SELECTOR_DROPDOWN_TOGGLE", "NOT_SELECTOR_DROPDOWN_TOGGLE", "SELECTOR_INNER_ELEM", "SELECTOR_DATA_TOGGLE_ACTIVE", "Tab", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "innerElem", "_elemIsActive", "active", "_getActiveElem", "hideEvent", "_deactivate", "_activate", "relatedElem", "_toggleDropDown", "nextActiveElement", "preventScroll", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "open", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "EVENT_FOCUSOUT", "CLASS_NAME_HIDE", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "isShown", "_onInteraction", "isInteracting"], "sources": ["../../js/src/dom/data.js", "../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/manipulator.js", "../../js/src/util/config.js", "../../js/src/base-component.js", "../../js/src/dom/selector-engine.js", "../../js/src/util/component-functions.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/util/swipe.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/util/backdrop.js", "../../js/src/util/focustrap.js", "../../js/src/util/scrollbar.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/util/template-factory.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1_000_000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n/**\n * Properly escape IDs selectors to handle weird IDs\n * @param {string} selector\n * @returns {string}\n */\nconst parseSelector = selector => {\n  if (selector && window.CSS && window.CSS.escape) {\n    // document.querySelector needs escaping to handle IDs (html5+) containing for instance /\n    selector = selector.replace(/#([^\\s\"#']+)/g, (match, id) => `#${CSS.escape(id)}`)\n  }\n\n  return selector\n}\n\n// Shout-out Angus Croll (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`\n  }\n\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false\n  }\n\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0]\n  }\n\n  return typeof object.nodeType !== 'undefined'\n}\n\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object\n  }\n\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(parseSelector(object))\n  }\n\n  return null\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])')\n\n  if (!closedDetails) {\n    return elementIsVisible\n  }\n\n  if (closedDetails !== element) {\n    const summary = element.closest('summary')\n    if (summary && summary.parentNode !== closedDetails) {\n      return false\n    }\n\n    if (summary === null) {\n      return false\n    }\n  }\n\n  return elementIsVisible\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.harrytheo.com/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight // eslint-disable-line no-unused-expressions\n}\n\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback()\n        }\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = (possibleCallback, args = [], defaultValue = possibleCallback) => {\n  return typeof possibleCallback === 'function' ? possibleCallback.call(...args) : defaultValue\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0]\n  }\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition,\n  findShadowRoot,\n  getElement,\n  getjQuery,\n  getNextActiveElement,\n  getTransitionDurationFromElement,\n  getUID,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  onDOMContentLoaded,\n  parseSelector,\n  reflow,\n  triggerTransitionEnd,\n  toType\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index.js'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction makeEventUid(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getElementEvents(element) {\n  const uid = makeEventUid(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    hydrateObj(event, { delegateTarget: element })\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        hydrateObj(event, { delegateTarget: target })\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, callable, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.callable === callable && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const isDelegated = typeof handler === 'string'\n  // TODO: tooltip passes `false` instead of selector, so we need to check\n  const callable = isDelegated ? delegationFunction : (handler || delegationFunction)\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [isDelegated, callable, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  let [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    callable = wrapFunction(callable)\n  }\n\n  const events = getElementEvents(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, callable, isDelegated ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = makeEventUid(callable, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = isDelegated ?\n    bootstrapDelegationHandler(element, handler, callable) :\n    bootstrapHandler(element, callable)\n\n  fn.delegationSelector = isDelegated ? handler : null\n  fn.callable = callable\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, isDelegated)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const [handlerKey, event] of Object.entries(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getElementEvents(element)\n    const storeElementEvent = events[typeEvent] || {}\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof callable !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!Object.keys(storeElementEvent).length) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, callable, isDelegated ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    for (const [keyHandlers, event] of Object.entries(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    const evt = hydrateObj(new Event(event, { bubbles, cancelable: true }), args)\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nfunction hydrateObj(obj, meta = {}) {\n  for (const [key, value] of Object.entries(meta)) {\n    try {\n      obj[key] = value\n    } catch {\n      Object.defineProperty(obj, key, {\n        configurable: true,\n        get() {\n          return value\n        }\n      })\n    }\n  }\n\n  return obj\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  if (value === Number(value).toString()) {\n    return Number(value)\n  }\n\n  if (value === '' || value === 'null') {\n    return null\n  }\n\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  try {\n    return JSON.parse(decodeURIComponent(value))\n  } catch {\n    return value\n  }\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'))\n\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '')\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1)\n      attributes[pureKey] = normalizeData(element.dataset[key])\n    }\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator.js'\nimport { isElement, toType } from './index.js'\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {}\n  }\n\n  static get DefaultType() {\n    return {}\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    return config\n  }\n\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {} // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    }\n  }\n\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const [property, expectedTypes] of Object.entries(configTypes)) {\n      const value = config[property]\n      const valueType = isElement(value) ? 'element' : toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(\n          `${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n        )\n      }\n    }\n  }\n}\n\nexport default Config\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data.js'\nimport EventHandler from './dom/event-handler.js'\nimport Config from './util/config.js'\nimport { executeAfterTransition, getElement } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst VERSION = '5.3.6'\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super()\n\n    element = getElement(element)\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    this._config = this._getConfig(config)\n\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null\n    }\n  }\n\n  // Private\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible, parseSelector } from '../util/index.js'\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || (!hrefAttribute.includes('#') && !hrefAttribute.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`\n    }\n\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null\n  }\n\n  return selector ? selector.split(',').map(sel => parseSelector(sel)).join(',') : null\n}\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  },\n\n  getSelectorFromElement(element) {\n    const selector = getSelector(element)\n\n    if (selector) {\n      return SelectorEngine.findOne(selector) ? selector : null\n    }\n\n    return null\n  },\n\n  getElementFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.findOne(selector) : null\n  },\n\n  getMultipleElementsFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.find(selector) : []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport { isDisabled } from './index.js'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = SelectorEngine.getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport { execute } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  endCallback: null,\n  leftCallback: null,\n  rightCallback: null\n}\n\nconst DefaultType = {\n  endCallback: '(function|null)',\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index.js'\nimport Swipe from './util/swipe.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)', // TODO:v6 remove boolean support\n  keyboard: 'boolean',\n  pause: '(string|boolean)',\n  ride: '(boolean|string)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // TODO: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  getElement,\n  reflow\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  parent: null,\n  toggle: true\n}\n\nconst DefaultType = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = SelectorEngine.getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = SelectorEngine.getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  // Private\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = SelectorEngine.getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  for (const element of SelectorEngine.getMultipleElementsFromSelector(this)) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  execute,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 2],\n  popperConfig: null,\n  reference: 'toggle'\n}\n\nconst DefaultType = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.prev(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n\n    // Explicitly return focus to the trigger element\n    this._element.focus()\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org/docs/v2/)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // TODO: v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [undefined, defaultBsPopperConfig])\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ?\n      this :\n      (SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.next(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode))\n\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport {\n  execute, executeAfterTransition, getElement, reflow\n} from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n}\n\nconst DefaultType = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n}\n\nconst DefaultType = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport { isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport FocusTrap from './util/focustrap.js'\nimport {\n  defineJQueryPlugin, isRTL, isVisible, reflow\n} from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  focus: true,\n  keyboard: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  focus: 'boolean',\n  keyboard: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    EventHandler.off(window, EVENT_KEY)\n    EventHandler.off(this._dialog, EVENT_KEY)\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_MOUSEDOWN_DISMISS, event => {\n      // a bad trick to segregate clicks that may start inside dialog but end outside, and avoid listen to scrollbar clicks\n      EventHandler.one(this._element, EVENT_CLICK_DISMISS, event2 => {\n        if (this._element !== event.target || this._element !== event2.target) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n          return\n        }\n\n        if (this._config.backdrop) {\n          this.hide()\n        }\n      })\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport FocusTrap from './util/focustrap.js'\nimport {\n  defineJQueryPlugin,\n  isDisabled,\n  isVisible\n} from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll || this._config.backdrop) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n// js-docs-start allow-list\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  dd: [],\n  div: [],\n  dl: [],\n  dt: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n// js-docs-end allow-list\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\n/**\n * A pattern that recognizes URLs that are safe wrt. XSS in URL navigation\n * contexts.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/15.2.8/packages/core/src/sanitization/url_sanitizer.ts#L38\n */\n// eslint-disable-next-line unicorn/better-regex\nconst SAFE_URL_PATTERN = /^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer.js'\nimport { execute, getElement, isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n}\n\nconst DefaultContentType = {\n  entry: '(string|element|function|null)',\n  selector: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [undefined, this])\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport {\n  defineJQueryPlugin, execute, findShadowRoot, getElement, getUID, isRTL, noop\n} from './util/index.js'\nimport { DefaultAllowlist } from './util/sanitizer.js'\nimport TemplateFactory from './util/template-factory.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 6],\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n            '<div class=\"tooltip-arrow\"></div>' +\n            '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  title: '',\n  trigger: 'hover focus'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org/docs/v2/)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = null\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n    this._newContent = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n\n    if (!this._config.selector) {\n      this._fixTitle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle() {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'))\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // TODO: v6 remove this or make it optional\n    this._disposePopper()\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    this._popper = this._createPopper(tip)\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (this._isHovered === false) {\n        this._leave()\n      }\n\n      this._isHovered = false\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = null // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        this._disposePopper()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // TODO: remove this check in v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // TODO: v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    this._newContent = content\n    if (this._isShown()) {\n      this._disposePopper()\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title')\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = execute(this._config.placement, [this, tip, this._element])\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this._element, this._element])\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [undefined, defaultBsPopperConfig])\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context.toggle()\n        })\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.setAttribute('data-bs-original-title', title) // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const [key, value] of Object.entries(this._config)) {\n      if (this.constructor.Default[key] !== value) {\n        config[key] = value\n      }\n    }\n\n    config.selector = false\n    config.trigger = 'manual'\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    if (this.tip) {\n      this.tip.remove()\n      this.tip = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Tooltip from './tooltip.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  content: '',\n  offset: [0, 8],\n  placement: 'right',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n    '<div class=\"popover-arrow\"></div>' +\n    '<h3 class=\"popover-header\"></h3>' +\n    '<div class=\"popover-body\"></div>' +\n    '</div>',\n  trigger: 'click'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin, getElement, isDisabled, isVisible\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null,\n  threshold: [0.1, 0.5, 1]\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element',\n  threshold: 'array'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n    config.rootMargin = config.offset ? `${config.offset}px 0px -30%` : config.rootMargin\n\n    if (typeof config.threshold === 'string') {\n      config.threshold = config.threshold.split(',').map(value => Number.parseFloat(value))\n    }\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height, behavior: 'smooth' })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: this._config.threshold,\n      rootMargin: this._config.rootMargin\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(decodeURI(anchor.hash), this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(decodeURI(anchor.hash), anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport { defineJQueryPlugin, getNextActiveElement, isDisabled } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst HOME_KEY = 'Home'\nconst END_KEY = 'End'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = `:not(${SELECTOR_DROPDOWN_TOGGLE})`\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // TODO: could only be `tab` in v6\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // TODO: should throw exception in v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(SelectorEngine.getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.add(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(SelectorEngine.getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.remove(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY, HOME_KEY, END_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n\n    const children = this._getChildren().filter(element => !isDisabled(element))\n    let nextActiveElement\n\n    if ([HOME_KEY, END_KEY].includes(event.key)) {\n      nextActiveElement = children[event.key === HOME_KEY ? 0 : children.length - 1]\n    } else {\n      const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n      nextActiveElement = getNextActiveElement(children, event.target, isNext, true)\n    }\n\n    if (nextActiveElement) {\n      nextActiveElement.focus({ preventScroll: true })\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = SelectorEngine.getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport { defineJQueryPlugin, reflow } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${D<PERSON>A_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout': {\n        this._hasMouseInteraction = isInteracting\n        break\n      }\n\n      case 'focusin':\n      case 'focusout': {\n        this._hasKeyboardInteraction = isInteracting\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert.js'\nimport Button from './src/button.js'\nimport Carousel from './src/carousel.js'\nimport Collapse from './src/collapse.js'\nimport Dropdown from './src/dropdown.js'\nimport Modal from './src/modal.js'\nimport Offcanvas from './src/offcanvas.js'\nimport Popover from './src/popover.js'\nimport ScrollSpy from './src/scrollspy.js'\nimport Tab from './src/tab.js'\nimport Toast from './src/toast.js'\nimport Tooltip from './src/tooltip.js'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"], "mappings": ";;;;;ujBAWMA,EAAa,IAAIC,IAEvBC,EAAe,CACbC,IAAIC,EAASC,EAAKC,GACXN,EAAWO,IAAIH,IAClBJ,EAAWG,IAAIC,EAAS,IAAIH,KAG9B,MAAMO,EAAcR,EAAWS,IAAIL,GAI9BI,EAAYD,IAAIF,IAA6B,IAArBG,EAAYE,KAMzCF,EAAYL,IAAIE,EAAKC,GAJnBK,QAAQC,MAAM,+EAA+EC,MAAMC,KAAKN,EAAYO,QAAQ,M,EAOhIN,IAAGA,CAACL,EAASC,IACPL,EAAWO,IAAIH,IACVJ,EAAWS,IAAIL,GAASK,IAAIJ,IAG9B,KAGTW,OAAOZ,EAASC,GACd,IAAKL,EAAWO,IAAIH,GAClB,OAGF,MAAMI,EAAcR,EAAWS,IAAIL,GAEnCI,EAAYS,OAAOZ,GAGM,IAArBG,EAAYE,MACdV,EAAWiB,OAAOb,EAEtB,GC5CIc,EAAiB,gBAOjBC,EAAgBC,IAChBA,GAAYC,OAAOC,KAAOD,OAAOC,IAAIC,SAEvCH,EAAWA,EAASI,QAAQ,iBAAiB,CAACC,EAAOC,IAAO,IAAIJ,IAAIC,OAAOG,QAGtEN,GA+CHO,EAAuBvB,IAC3BA,EAAQwB,cAAc,IAAIC,MAAMX,GAAgB,EAG5CY,EAAYC,MACXA,GAA4B,iBAAXA,UAIO,IAAlBA,EAAOC,SAChBD,EAASA,EAAO,SAGgB,IAApBA,EAAOE,UAGjBC,EAAaH,GAEbD,EAAUC,GACLA,EAAOC,OAASD,EAAO,GAAKA,EAGf,iBAAXA,GAAuBA,EAAOI,OAAS,EACzCC,SAASC,cAAclB,EAAcY,IAGvC,KAGHO,EAAYlC,IAChB,IAAK0B,EAAU1B,IAAgD,IAApCA,EAAQmC,iBAAiBJ,OAClD,OAAO,EAGT,MAAMK,EAAgF,YAA7DC,iBAAiBrC,GAASsC,iBAAiB,cAE9DC,EAAgBvC,EAAQwC,QAAQ,uBAEtC,IAAKD,EACH,OAAOH,EAGT,GAAIG,IAAkBvC,EAAS,CAC7B,MAAMyC,EAAUzC,EAAQwC,QAAQ,WAChC,GAAIC,GAAWA,EAAQC,aAAeH,EACpC,OAAO,EAGT,GAAgB,OAAZE,EACF,OAAO,CAEX,CAEA,OAAOL,CAAgB,EAGnBO,EAAa3C,IACZA,GAAWA,EAAQ6B,WAAae,KAAKC,gBAItC7C,EAAQ8C,UAAUC,SAAS,mBAIC,IAArB/C,EAAQgD,SACVhD,EAAQgD,SAGVhD,EAAQiD,aAAa,aAAoD,UAArCjD,EAAQkD,aAAa,aAG5DC,EAAiBnD,IACrB,IAAKgC,SAASoB,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBrD,EAAQsD,YAA4B,CAC7C,MAAMC,EAAOvD,EAAQsD,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,IAC7C,CAEA,OAAIvD,aAAmBwD,WACdxD,EAIJA,EAAQ0C,WAINS,EAAenD,EAAQ0C,YAHrB,IAGgC,EAGrCe,EAAOA,OAUPC,EAAS1D,IACbA,EAAQ2D,YAAY,EAGhBC,EAAYA,IACZ3C,OAAO4C,SAAW7B,SAAS8B,KAAKb,aAAa,qBACxChC,OAAO4C,OAGT,KAGHE,EAA4B,GAmB5BC,EAAQA,IAAuC,QAAjChC,SAASoB,gBAAgBa,IAEvCC,EAAqBC,IAnBAC,QAoBN,KACjB,MAAMC,EAAIT,IAEV,GAAIS,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,gBAElB,GA/B0B,YAAxB1C,SAAS6C,YAENd,EAA0BhC,QAC7BC,SAAS8C,iBAAiB,oBAAoB,KAC5C,IAAK,MAAMV,KAAYL,EACrBK,GACF,IAIJL,EAA0BgB,KAAKX,IAE/BA,GAoBA,EAGEY,EAAUA,CAACC,EAAkBC,EAAO,GAAIC,EAAeF,IACxB,mBAArBA,EAAkCA,EAAiBG,QAAQF,GAAQC,EAG7EE,EAAyBA,CAACjB,EAAUkB,EAAmBC,GAAoB,KAC/E,IAAKA,EAEH,YADAP,EAAQZ,GAIV,MACMoB,EA7LiCxF,KACvC,IAAKA,EACH,OAAO,EAIT,IAAIyF,mBAAEA,EAAkBC,gBAAEA,GAAoBzE,OAAOoB,iBAAiBrC,GAEtE,MAAM2F,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBM,MAAM,KAAK,GACnDL,EAAkBA,EAAgBK,MAAM,KAAK,GAxDf,KA0DtBH,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,CAOoG,EAyKpFM,CAAiCV,GADlC,EAGxB,IAAIW,GAAS,EAEb,MAAMC,EAAUA,EAAGC,aACbA,IAAWb,IAIfW,GAAS,EACTX,EAAkBc,oBAAoBtF,EAAgBoF,GACtDlB,EAAQZ,GAAS,EAGnBkB,EAAkBR,iBAAiBhE,EAAgBoF,GACnDG,YAAW,KACJJ,GACH1E,EAAqB+D,EACvB,GACCE,EAAiB,EAYhBc,EAAuBA,CAACC,EAAMC,EAAeC,EAAeC,KAChE,MAAMC,EAAaJ,EAAKxE,OACxB,IAAI6E,EAAQL,EAAKM,QAAQL,GAIzB,OAAc,IAAVI,GACMH,GAAiBC,EAAiBH,EAAKI,EAAa,GAAKJ,EAAK,IAGxEK,GAASH,EAAgB,GAAI,EAEzBC,IACFE,GAASA,EAAQD,GAAcA,GAG1BJ,EAAKO,KAAKC,IAAI,EAAGD,KAAKE,IAAIJ,EAAOD,EAAa,KAAI,EC7QrDM,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAGRC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WAOF,SAASC,EAAa3H,EAAS4H,GAC7B,OAAQA,GAAO,GAAGA,MAAQP,OAAiBrH,EAAQqH,UAAYA,GACjE,CAEA,SAASQ,EAAiB7H,GACxB,MAAM4H,EAAMD,EAAa3H,GAKzB,OAHAA,EAAQqH,SAAWO,EACnBR,EAAcQ,GAAOR,EAAcQ,IAAQ,GAEpCR,EAAcQ,EACvB,CAoCA,SAASE,EAAYC,EAAQC,EAAUC,EAAqB,MAC1D,OAAOC,OAAOC,OAAOJ,GAClBK,MAAKC,GAASA,EAAML,WAAaA,GAAYK,EAAMJ,qBAAuBA,GAC/E,CAEA,SAASK,EAAoBC,EAAmBrC,EAASsC,GACvD,MAAMC,EAAiC,iBAAZvC,EAErB8B,EAAWS,EAAcD,EAAsBtC,GAAWsC,EAChE,IAAIE,EAAYC,EAAaJ,GAM7B,OAJKd,EAAatH,IAAIuI,KACpBA,EAAYH,GAGP,CAACE,EAAaT,EAAUU,EACjC,CAEA,SAASE,EAAW5I,EAASuI,EAAmBrC,EAASsC,EAAoBK,GAC3E,GAAiC,iBAAtBN,IAAmCvI,EAC5C,OAGF,IAAKyI,EAAaT,EAAUU,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GAIzF,GAAID,KAAqBjB,EAAc,CACrC,MAAMwB,EAAerE,GACZ,SAAU4D,GACf,IAAKA,EAAMU,eAAkBV,EAAMU,gBAAkBV,EAAMW,iBAAmBX,EAAMW,eAAejG,SAASsF,EAAMU,eAChH,OAAOtE,EAAGW,KAAK6D,KAAMZ,E,EAK3BL,EAAWc,EAAad,EAC1B,CAEA,MAAMD,EAASF,EAAiB7H,GAC1BkJ,EAAWnB,EAAOW,KAAeX,EAAOW,GAAa,IACrDS,EAAmBrB,EAAYoB,EAAUlB,EAAUS,EAAcvC,EAAU,MAEjF,GAAIiD,EAGF,YAFAA,EAAiBN,OAASM,EAAiBN,QAAUA,GAKvD,MAAMjB,EAAMD,EAAaK,EAAUO,EAAkBnH,QAAQ6F,EAAgB,KACvExC,EAAKgE,EAxEb,SAAoCzI,EAASgB,EAAUyD,GACrD,OAAO,SAASyB,EAAQmC,GACtB,MAAMe,EAAcpJ,EAAQqJ,iBAAiBrI,GAE7C,IAAK,IAAImF,OAAEA,GAAWkC,EAAOlC,GAAUA,IAAW8C,KAAM9C,EAASA,EAAOzD,WACtE,IAAK,MAAM4G,KAAcF,EACvB,GAAIE,IAAenD,EAUnB,OANAoD,EAAWlB,EAAO,CAAEW,eAAgB7C,IAEhCD,EAAQ2C,QACVW,EAAaC,IAAIzJ,EAASqI,EAAMqB,KAAM1I,EAAUyD,GAG3CA,EAAGkF,MAAMxD,EAAQ,CAACkC,G,CAIjC,CAqDIuB,CAA2B5J,EAASkG,EAAS8B,GArFjD,SAA0BhI,EAASyE,GACjC,OAAO,SAASyB,EAAQmC,GAOtB,OANAkB,EAAWlB,EAAO,CAAEW,eAAgBhJ,IAEhCkG,EAAQ2C,QACVW,EAAaC,IAAIzJ,EAASqI,EAAMqB,KAAMjF,GAGjCA,EAAGkF,MAAM3J,EAAS,CAACqI,G,CAE9B,CA4EIwB,CAAiB7J,EAASgI,GAE5BvD,EAAGwD,mBAAqBQ,EAAcvC,EAAU,KAChDzB,EAAGuD,SAAWA,EACdvD,EAAGoE,OAASA,EACZpE,EAAG4C,SAAWO,EACdsB,EAAStB,GAAOnD,EAEhBzE,EAAQ8E,iBAAiB4D,EAAWjE,EAAIgE,EAC1C,CAEA,SAASqB,EAAc9J,EAAS+H,EAAQW,EAAWxC,EAAS+B,GAC1D,MAAMxD,EAAKqD,EAAYC,EAAOW,GAAYxC,EAAS+B,GAE9CxD,IAILzE,EAAQoG,oBAAoBsC,EAAWjE,EAAIsF,QAAQ9B,WAC5CF,EAAOW,GAAWjE,EAAG4C,UAC9B,CAEA,SAAS2C,EAAyBhK,EAAS+H,EAAQW,EAAWuB,GAC5D,MAAMC,EAAoBnC,EAAOW,IAAc,GAE/C,IAAK,MAAOyB,EAAY9B,KAAUH,OAAOkC,QAAQF,GAC3CC,EAAWE,SAASJ,IACtBH,EAAc9J,EAAS+H,EAAQW,EAAWL,EAAML,SAAUK,EAAMJ,mBAGtE,CAEA,SAASU,EAAaN,GAGpB,OADAA,EAAQA,EAAMjH,QAAQ8F,EAAgB,IAC/BI,EAAae,IAAUA,CAChC,CAEA,MAAMmB,EAAe,CACnBc,GAAGtK,EAASqI,EAAOnC,EAASsC,GAC1BI,EAAW5I,EAASqI,EAAOnC,EAASsC,GAAoB,E,EAG1D+B,IAAIvK,EAASqI,EAAOnC,EAASsC,GAC3BI,EAAW5I,EAASqI,EAAOnC,EAASsC,GAAoB,E,EAG1DiB,IAAIzJ,EAASuI,EAAmBrC,EAASsC,GACvC,GAAiC,iBAAtBD,IAAmCvI,EAC5C,OAGF,MAAOyI,EAAaT,EAAUU,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GACrFgC,EAAc9B,IAAcH,EAC5BR,EAASF,EAAiB7H,GAC1BkK,EAAoBnC,EAAOW,IAAc,GACzC+B,EAAclC,EAAkBmC,WAAW,KAEjD,QAAwB,IAAb1C,EAAX,CAUA,GAAIyC,EACF,IAAK,MAAME,KAAgBzC,OAAOvH,KAAKoH,GACrCiC,EAAyBhK,EAAS+H,EAAQ4C,EAAcpC,EAAkBqC,MAAM,IAIpF,IAAK,MAAOC,EAAaxC,KAAUH,OAAOkC,QAAQF,GAAoB,CACpE,MAAMC,EAAaU,EAAYzJ,QAAQ+F,EAAe,IAEjDqD,IAAejC,EAAkB8B,SAASF,IAC7CL,EAAc9J,EAAS+H,EAAQW,EAAWL,EAAML,SAAUK,EAAMJ,mBAEpE,CAdA,KARA,CAEE,IAAKC,OAAOvH,KAAKuJ,GAAmBnI,OAClC,OAGF+H,EAAc9J,EAAS+H,EAAQW,EAAWV,EAAUS,EAAcvC,EAAU,KAE9E,C,EAiBF4E,QAAQ9K,EAASqI,EAAOnD,GACtB,GAAqB,iBAAVmD,IAAuBrI,EAChC,OAAO,KAGT,MAAMqE,EAAIT,IAIV,IAAImH,EAAc,KACdC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EALH7C,IADFM,EAAaN,IAQZhE,IACjB0G,EAAc1G,EAAE5C,MAAM4G,EAAOnD,GAE7Bb,EAAErE,GAAS8K,QAAQC,GACnBC,GAAWD,EAAYI,uBACvBF,GAAkBF,EAAYK,gCAC9BF,EAAmBH,EAAYM,sBAGjC,MAAMC,EAAM/B,EAAW,IAAI9H,MAAM4G,EAAO,CAAE2C,UAASO,YAAY,IAASrG,GAcxE,OAZIgG,GACFI,EAAIE,iBAGFP,GACFjL,EAAQwB,cAAc8J,GAGpBA,EAAIJ,kBAAoBH,GAC1BA,EAAYS,iBAGPF,CACT,GAGF,SAAS/B,EAAWkC,EAAKC,EAAO,IAC9B,IAAK,MAAOzL,EAAK0L,KAAUzD,OAAOkC,QAAQsB,GACxC,IACED,EAAIxL,GAAO0L,C,CACX,MAAAC,GACA1D,OAAO2D,eAAeJ,EAAKxL,EAAK,CAC9B6L,cAAc,EACdzL,IAAGA,IACMsL,GAGb,CAGF,OAAOF,CACT,CCnTA,SAASM,EAAcJ,GACrB,GAAc,SAAVA,EACF,OAAO,EAGT,GAAc,UAAVA,EACF,OAAO,EAGT,GAAIA,IAAU/F,OAAO+F,GAAOK,WAC1B,OAAOpG,OAAO+F,GAGhB,GAAc,KAAVA,GAA0B,SAAVA,EAClB,OAAO,KAGT,GAAqB,iBAAVA,EACT,OAAOA,EAGT,IACE,OAAOM,KAAKC,MAAMC,mBAAmBR,G,CACrC,MAAAC,GACA,OAAOD,CACT,CACF,CAEA,SAASS,EAAiBnM,GACxB,OAAOA,EAAImB,QAAQ,UAAUiL,GAAO,IAAIA,EAAIC,iBAC9C,CAEA,MAAMC,EAAc,CAClBC,iBAAiBxM,EAASC,EAAK0L,GAC7B3L,EAAQyM,aAAa,WAAWL,EAAiBnM,KAAQ0L,E,EAG3De,oBAAoB1M,EAASC,GAC3BD,EAAQ2M,gBAAgB,WAAWP,EAAiBnM,K,EAGtD2M,kBAAkB5M,GAChB,IAAKA,EACH,MAAO,GAGT,MAAM6M,EAAa,GACbC,EAAS5E,OAAOvH,KAAKX,EAAQ+M,SAASC,QAAO/M,GAAOA,EAAIyK,WAAW,QAAUzK,EAAIyK,WAAW,cAElG,IAAK,MAAMzK,KAAO6M,EAAQ,CACxB,IAAIG,EAAUhN,EAAImB,QAAQ,MAAO,IACjC6L,EAAUA,EAAQC,OAAO,GAAGZ,cAAgBW,EAAQrC,MAAM,GAC1DiC,EAAWI,GAAWlB,EAAc/L,EAAQ+M,QAAQ9M,GACtD,CAEA,OAAO4M,C,EAGTM,iBAAgBA,CAACnN,EAASC,IACjB8L,EAAc/L,EAAQkD,aAAa,WAAWkJ,EAAiBnM,QCpD1E,MAAMmN,EAEJ,kBAAWC,GACT,MAAO,EACT,CAEA,sBAAWC,GACT,MAAO,EACT,CAEA,eAAW/I,GACT,MAAM,IAAIgJ,MAAM,sEAClB,CAEAC,WAAWC,GAIT,OAHAA,EAASxE,KAAKyE,gBAAgBD,GAC9BA,EAASxE,KAAK0E,kBAAkBF,GAChCxE,KAAK2E,iBAAiBH,GACfA,CACT,CAEAE,kBAAkBF,GAChB,OAAOA,CACT,CAEAC,gBAAgBD,EAAQzN,GACtB,MAAM6N,EAAanM,EAAU1B,GAAWuM,EAAYY,iBAAiBnN,EAAS,UAAY,GAE1F,MAAO,IACFiJ,KAAK6E,YAAYT,WACM,iBAAfQ,EAA0BA,EAAa,MAC9CnM,EAAU1B,GAAWuM,EAAYK,kBAAkB5M,GAAW,MAC5C,iBAAXyN,EAAsBA,EAAS,GAE9C,CAEAG,iBAAiBH,EAAQM,EAAc9E,KAAK6E,YAAYR,aACtD,IAAK,MAAOU,EAAUC,KAAkB/F,OAAOkC,QAAQ2D,GAAc,CACnE,MAAMpC,EAAQ8B,EAAOO,GACfE,EAAYxM,EAAUiK,GAAS,UH1BrChK,OADSA,EG2B+CgK,GHzBnD,GAAGhK,IAGLuG,OAAOiG,UAAUnC,SAAS5G,KAAKzD,GAAQN,MAAM,eAAe,GAAGiL,cGwBlE,IAAK,IAAI8B,OAAOH,GAAeI,KAAKH,GAClC,MAAM,IAAII,UACR,GAAGrF,KAAK6E,YAAYvJ,KAAKgK,0BAA0BP,qBAA4BE,yBAAiCD,MAGtH,CHlCWtM,KGmCb,ECvCF,MAAM6M,UAAsBpB,EAC1BU,YAAY9N,EAASyN,GACnBgB,SAEAzO,EAAU8B,EAAW9B,MAKrBiJ,KAAKyF,SAAW1O,EAChBiJ,KAAK0F,QAAU1F,KAAKuE,WAAWC,GAE/B3N,EAAKC,IAAIkJ,KAAKyF,SAAUzF,KAAK6E,YAAYc,SAAU3F,MACrD,CAGA4F,UACE/O,EAAKc,OAAOqI,KAAKyF,SAAUzF,KAAK6E,YAAYc,UAC5CpF,EAAaC,IAAIR,KAAKyF,SAAUzF,KAAK6E,YAAYgB,WAEjD,IAAK,MAAMC,KAAgB7G,OAAO8G,oBAAoB/F,MACpDA,KAAK8F,GAAgB,IAEzB,CAGAE,eAAe7K,EAAUpE,EAASkP,GAAa,GAC7C7J,EAAuBjB,EAAUpE,EAASkP,EAC5C,CAEA1B,WAAWC,GAIT,OAHAA,EAASxE,KAAKyE,gBAAgBD,EAAQxE,KAAKyF,UAC3CjB,EAASxE,KAAK0E,kBAAkBF,GAChCxE,KAAK2E,iBAAiBH,GACfA,CACT,CAGA,kBAAO0B,CAAYnP,GACjB,OAAOF,EAAKO,IAAIyB,EAAW9B,GAAUiJ,KAAK2F,SAC5C,CAEA,0BAAOQ,CAAoBpP,EAASyN,EAAS,IAC3C,OAAOxE,KAAKkG,YAAYnP,IAAY,IAAIiJ,KAAKjJ,EAA2B,iBAAXyN,EAAsBA,EAAS,KAC9F,CAEA,kBAAW4B,GACT,MArDY,OAsDd,CAEA,mBAAWT,GACT,MAAO,MAAM3F,KAAK1E,MACpB,CAEA,oBAAWuK,GACT,MAAO,IAAI7F,KAAK2F,UAClB,CAEA,gBAAOU,CAAUhL,GACf,MAAO,GAAGA,IAAO2E,KAAK6F,WACxB,ECzEF,MAAMS,EAAcvP,IAClB,IAAIgB,EAAWhB,EAAQkD,aAAa,kBAEpC,IAAKlC,GAAyB,MAAbA,EAAkB,CACjC,IAAIwO,EAAgBxP,EAAQkD,aAAa,QAMzC,IAAKsM,IAAmBA,EAAcnF,SAAS,OAASmF,EAAc9E,WAAW,KAC/E,OAAO,KAIL8E,EAAcnF,SAAS,OAASmF,EAAc9E,WAAW,OAC3D8E,EAAgB,IAAIA,EAAczJ,MAAM,KAAK,MAG/C/E,EAAWwO,GAAmC,MAAlBA,EAAwBA,EAAcC,OAAS,IAC7E,CAEA,OAAOzO,EAAWA,EAAS+E,MAAM,KAAK2J,KAAIC,GAAO5O,EAAc4O,KAAMC,KAAK,KAAO,IAAI,EAGjFC,EAAiB,CACrBzH,KAAIA,CAACpH,EAAUhB,EAAUgC,SAASoB,kBACzB,GAAG0M,UAAUC,QAAQ5B,UAAU9E,iBAAiBjE,KAAKpF,EAASgB,IAGvEgP,QAAOA,CAAChP,EAAUhB,EAAUgC,SAASoB,kBAC5B2M,QAAQ5B,UAAUlM,cAAcmD,KAAKpF,EAASgB,GAGvDiP,SAAQA,CAACjQ,EAASgB,IACT,GAAG8O,UAAU9P,EAAQiQ,UAAUjD,QAAOkD,GAASA,EAAMC,QAAQnP,KAGtEoP,QAAQpQ,EAASgB,GACf,MAAMoP,EAAU,GAChB,IAAIC,EAAWrQ,EAAQ0C,WAAWF,QAAQxB,GAE1C,KAAOqP,GACLD,EAAQrL,KAAKsL,GACbA,EAAWA,EAAS3N,WAAWF,QAAQxB,GAGzC,OAAOoP,C,EAGTE,KAAKtQ,EAASgB,GACZ,IAAIuP,EAAWvQ,EAAQwQ,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASJ,QAAQnP,GACnB,MAAO,CAACuP,GAGVA,EAAWA,EAASC,sBACtB,CAEA,MAAO,E,EAGTC,KAAKzQ,EAASgB,GACZ,IAAIyP,EAAOzQ,EAAQ0Q,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKN,QAAQnP,GACf,MAAO,CAACyP,GAGVA,EAAOA,EAAKC,kBACd,CAEA,MAAO,E,EAGTC,kBAAkB3Q,GAChB,MAAM4Q,EAAa,CACjB,IACA,SACA,QACA,WACA,SACA,UACA,aACA,4BACAlB,KAAI1O,GAAY,GAAGA,2BAAiC4O,KAAK,KAE3D,OAAO3G,KAAKb,KAAKwI,EAAY5Q,GAASgN,QAAO6D,IAAOlO,EAAWkO,IAAO3O,EAAU2O,I,EAGlFC,uBAAuB9Q,GACrB,MAAMgB,EAAWuO,EAAYvP,GAE7B,OAAIgB,GACK6O,EAAeG,QAAQhP,GAAYA,EAGrC,I,EAGT+P,uBAAuB/Q,GACrB,MAAMgB,EAAWuO,EAAYvP,GAE7B,OAAOgB,EAAW6O,EAAeG,QAAQhP,GAAY,I,EAGvDgQ,gCAAgChR,GAC9B,MAAMgB,EAAWuO,EAAYvP,GAE7B,OAAOgB,EAAW6O,EAAezH,KAAKpH,GAAY,EACpD,GC/GIiQ,EAAuBA,CAACC,EAAWC,EAAS,UAChD,MAAMC,EAAa,gBAAgBF,EAAUpC,YACvCxK,EAAO4M,EAAU3M,KAEvBiF,EAAac,GAAGtI,SAAUoP,EAAY,qBAAqB9M,OAAU,SAAU+D,GAK7E,GAJI,CAAC,IAAK,QAAQgC,SAASpB,KAAKoI,UAC9BhJ,EAAMmD,iBAGJ7I,EAAWsG,MACb,OAGF,MAAM9C,EAAS0J,EAAekB,uBAAuB9H,OAASA,KAAKzG,QAAQ,IAAI8B,KAC9D4M,EAAU9B,oBAAoBjJ,GAGtCgL,IACX,GAAE,ECXErC,EAAY,YAEZwC,EAAc,QAAQxC,IACtByC,EAAe,SAASzC,IAQ9B,MAAM0C,UAAchD,EAElB,eAAWjK,GACT,MAhBS,OAiBX,CAGAkN,QAGE,GAFmBjI,EAAasB,QAAQ7B,KAAKyF,SAAU4C,GAExCpG,iBACb,OAGFjC,KAAKyF,SAAS5L,UAAUlC,OApBJ,QAsBpB,MAAMsO,EAAajG,KAAKyF,SAAS5L,UAAUC,SAvBvB,QAwBpBkG,KAAKgG,gBAAe,IAAMhG,KAAKyI,mBAAmBzI,KAAKyF,SAAUQ,EACnE,CAGAwC,kBACEzI,KAAKyF,SAAS9N,SACd4I,EAAasB,QAAQ7B,KAAKyF,SAAU6C,GACpCtI,KAAK4F,SACP,CAGA,sBAAOnK,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOJ,EAAMpC,oBAAoBnG,MAEvC,GAAsB,iBAAXwE,EAAX,CAIA,QAAqBoE,IAAjBD,EAAKnE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,GAAQxE,KANb,CAOF,GACF,EAOFgI,EAAqBO,EAAO,SAM5BtN,EAAmBsN,GCrEnB,MAMMM,EAAuB,4BAO7B,MAAMC,UAAevD,EAEnB,eAAWjK,GACT,MAhBS,QAiBX,CAGAyN,SAEE/I,KAAKyF,SAASjC,aAAa,eAAgBxD,KAAKyF,SAAS5L,UAAUkP,OAjB7C,UAkBxB,CAGA,sBAAOtN,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOG,EAAO3C,oBAAoBnG,MAEzB,WAAXwE,GACFmE,EAAKnE,IAET,GACF,EAOFjE,EAAac,GAAGtI,SAlCa,2BAkCmB8P,GAAsBzJ,IACpEA,EAAMmD,iBAEN,MAAMyG,EAAS5J,EAAMlC,OAAO3D,QAAQsP,GACvBC,EAAO3C,oBAAoB6C,GAEnCD,QAAQ,IAOf9N,EAAmB6N,GCtDnB,MACMjD,EAAY,YACZoD,GAAmB,aAAapD,IAChCqD,GAAkB,YAAYrD,IAC9BsD,GAAiB,WAAWtD,IAC5BuD,GAAoB,cAAcvD,IAClCwD,GAAkB,YAAYxD,IAM9BzB,GAAU,CACdkF,YAAa,KACbC,aAAc,KACdC,cAAe,MAGXnF,GAAc,CAClBiF,YAAa,kBACbC,aAAc,kBACdC,cAAe,mBAOjB,MAAMC,WAActF,EAClBU,YAAY9N,EAASyN,GACnBgB,QACAxF,KAAKyF,SAAW1O,EAEXA,GAAY0S,GAAMC,gBAIvB1J,KAAK0F,QAAU1F,KAAKuE,WAAWC,GAC/BxE,KAAK2J,QAAU,EACf3J,KAAK4J,sBAAwB9I,QAAQ9I,OAAO6R,cAC5C7J,KAAK8J,cACP,CAGA,kBAAW1F,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MArDS,OAsDX,CAGAsK,UACErF,EAAaC,IAAIR,KAAKyF,SAAUI,EAClC,CAGAkE,OAAO3K,GACAY,KAAK4J,sBAMN5J,KAAKgK,wBAAwB5K,KAC/BY,KAAK2J,QAAUvK,EAAM6K,SANrBjK,KAAK2J,QAAUvK,EAAM8K,QAAQ,GAAGD,OAQpC,CAEAE,KAAK/K,GACCY,KAAKgK,wBAAwB5K,KAC/BY,KAAK2J,QAAUvK,EAAM6K,QAAUjK,KAAK2J,SAGtC3J,KAAKoK,eACLrO,EAAQiE,KAAK0F,QAAQ4D,YACvB,CAEAe,MAAMjL,GACJY,KAAK2J,QAAUvK,EAAM8K,SAAW9K,EAAM8K,QAAQpR,OAAS,EACrD,EACAsG,EAAM8K,QAAQ,GAAGD,QAAUjK,KAAK2J,OACpC,CAEAS,eACE,MAAME,EAAYzM,KAAK0M,IAAIvK,KAAK2J,SAEhC,GAAIW,GAlFgB,GAmFlB,OAGF,MAAME,EAAYF,EAAYtK,KAAK2J,QAEnC3J,KAAK2J,QAAU,EAEVa,GAILzO,EAAQyO,EAAY,EAAIxK,KAAK0F,QAAQ8D,cAAgBxJ,KAAK0F,QAAQ6D,aACpE,CAEAO,cACM9J,KAAK4J,uBACPrJ,EAAac,GAAGrB,KAAKyF,SAAU2D,IAAmBhK,GAASY,KAAK+J,OAAO3K,KACvEmB,EAAac,GAAGrB,KAAKyF,SAAU4D,IAAiBjK,GAASY,KAAKmK,KAAK/K,KAEnEY,KAAKyF,SAAS5L,UAAU4Q,IAvGG,mBAyG3BlK,EAAac,GAAGrB,KAAKyF,SAAUwD,IAAkB7J,GAASY,KAAK+J,OAAO3K,KACtEmB,EAAac,GAAGrB,KAAKyF,SAAUyD,IAAiB9J,GAASY,KAAKqK,MAAMjL,KACpEmB,EAAac,GAAGrB,KAAKyF,SAAU0D,IAAgB/J,GAASY,KAAKmK,KAAK/K,KAEtE,CAEA4K,wBAAwB5K,GACtB,OAAOY,KAAK4J,wBAjHS,QAiHiBxK,EAAMsL,aAlHrB,UAkHyDtL,EAAMsL,YACxF,CAGA,kBAAOhB,GACL,MAAO,iBAAkB3Q,SAASoB,iBAAmBwQ,UAAUC,eAAiB,CAClF,ECrHF,MAEM/E,GAAY,eACZgF,GAAe,YAEfC,GAAiB,YACjBC,GAAkB,aAGlBC,GAAa,OACbC,GAAa,OACbC,GAAiB,OACjBC,GAAkB,QAElBC,GAAc,QAAQvF,KACtBwF,GAAa,OAAOxF,KACpByF,GAAgB,UAAUzF,KAC1B0F,GAAmB,aAAa1F,KAChC2F,GAAmB,aAAa3F,KAChC4F,GAAmB,YAAY5F,KAC/B6F,GAAsB,OAAO7F,KAAYgF,KACzCc,GAAuB,QAAQ9F,KAAYgF,KAE3Ce,GAAsB,WACtBC,GAAoB,SAOpBC,GAAkB,UAClBC,GAAgB,iBAChBC,GAAuBF,GAAkBC,GAMzCE,GAAmB,CACvBC,CAACpB,IAAiBK,GAClBgB,CAACpB,IAAkBG,IAGf9G,GAAU,CACdgI,SAAU,IACVC,UAAU,EACVC,MAAO,QACPC,MAAM,EACNC,OAAO,EACPC,MAAM,GAGFpI,GAAc,CAClB+H,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,KAAM,mBACNC,MAAO,UACPC,KAAM,WAOR,MAAMC,WAAiBnH,EACrBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAK2M,UAAY,KACjB3M,KAAK4M,eAAiB,KACtB5M,KAAK6M,YAAa,EAClB7M,KAAK8M,aAAe,KACpB9M,KAAK+M,aAAe,KAEpB/M,KAAKgN,mBAAqBpG,EAAeG,QAzCjB,uBAyC8C/G,KAAKyF,UAC3EzF,KAAKiN,qBAEDjN,KAAK0F,QAAQ6G,OAASX,IACxB5L,KAAKkN,OAET,CAGA,kBAAW9I,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MA9FS,UA+FX,CAGAkM,OACExH,KAAKmN,OAAOnC,GACd,CAEAoC,mBAIOrU,SAASsU,QAAUpU,EAAU+G,KAAKyF,WACrCzF,KAAKwH,MAET,CAEAH,OACErH,KAAKmN,OAAOlC,GACd,CAEAqB,QACMtM,KAAK6M,YACPvU,EAAqB0H,KAAKyF,UAG5BzF,KAAKsN,gBACP,CAEAJ,QACElN,KAAKsN,iBACLtN,KAAKuN,kBAELvN,KAAK2M,UAAYa,aAAY,IAAMxN,KAAKoN,mBAAmBpN,KAAK0F,QAAQ0G,SAC1E,CAEAqB,oBACOzN,KAAK0F,QAAQ6G,OAIdvM,KAAK6M,WACPtM,EAAae,IAAItB,KAAKyF,SAAU4F,IAAY,IAAMrL,KAAKkN,UAIzDlN,KAAKkN,QACP,CAEAQ,GAAG/P,GACD,MAAMgQ,EAAQ3N,KAAK4N,YACnB,GAAIjQ,EAAQgQ,EAAM7U,OAAS,GAAK6E,EAAQ,EACtC,OAGF,GAAIqC,KAAK6M,WAEP,YADAtM,EAAae,IAAItB,KAAKyF,SAAU4F,IAAY,IAAMrL,KAAK0N,GAAG/P,KAI5D,MAAMkQ,EAAc7N,KAAK8N,cAAc9N,KAAK+N,cAC5C,GAAIF,IAAgBlQ,EAClB,OAGF,MAAMqQ,EAAQrQ,EAAQkQ,EAAc7C,GAAaC,GAEjDjL,KAAKmN,OAAOa,EAAOL,EAAMhQ,GAC3B,CAEAiI,UACM5F,KAAK+M,cACP/M,KAAK+M,aAAanH,UAGpBJ,MAAMI,SACR,CAGAlB,kBAAkBF,GAEhB,OADAA,EAAOyJ,gBAAkBzJ,EAAO4H,SACzB5H,CACT,CAEAyI,qBACMjN,KAAK0F,QAAQ2G,UACf9L,EAAac,GAAGrB,KAAKyF,SAAU6F,IAAelM,GAASY,KAAKkO,SAAS9O,KAG5C,UAAvBY,KAAK0F,QAAQ4G,QACf/L,EAAac,GAAGrB,KAAKyF,SAAU8F,IAAkB,IAAMvL,KAAKsM,UAC5D/L,EAAac,GAAGrB,KAAKyF,SAAU+F,IAAkB,IAAMxL,KAAKyN,uBAG1DzN,KAAK0F,QAAQ8G,OAAS/C,GAAMC,eAC9B1J,KAAKmO,yBAET,CAEAA,0BACE,IAAK,MAAMC,KAAOxH,EAAezH,KAhKX,qBAgKmCa,KAAKyF,UAC5DlF,EAAac,GAAG+M,EAAK3C,IAAkBrM,GAASA,EAAMmD,mBAGxD,MAqBM8L,EAAc,CAClB9E,aAAcA,IAAMvJ,KAAKmN,OAAOnN,KAAKsO,kBAAkBpD,KACvD1B,cAAeA,IAAMxJ,KAAKmN,OAAOnN,KAAKsO,kBAAkBnD,KACxD7B,YAxBkBiF,KACS,UAAvBvO,KAAK0F,QAAQ4G,QAYjBtM,KAAKsM,QACDtM,KAAK8M,cACP0B,aAAaxO,KAAK8M,cAGpB9M,KAAK8M,aAAe1P,YAAW,IAAM4C,KAAKyN,qBAjNjB,IAiN+DzN,KAAK0F,QAAQ0G,UAAS,GAShHpM,KAAK+M,aAAe,IAAItD,GAAMzJ,KAAKyF,SAAU4I,EAC/C,CAEAH,SAAS9O,GACP,GAAI,kBAAkBgG,KAAKhG,EAAMlC,OAAOkL,SACtC,OAGF,MAAMoC,EAAYyB,GAAiB7M,EAAMpI,KACrCwT,IACFpL,EAAMmD,iBACNvC,KAAKmN,OAAOnN,KAAKsO,kBAAkB9D,IAEvC,CAEAsD,cAAc/W,GACZ,OAAOiJ,KAAK4N,YAAYhQ,QAAQ7G,EAClC,CAEA0X,2BAA2B9Q,GACzB,IAAKqC,KAAKgN,mBACR,OAGF,MAAM0B,EAAkB9H,EAAeG,QAAQ+E,GAAiB9L,KAAKgN,oBAErE0B,EAAgB7U,UAAUlC,OAAOkU,IACjC6C,EAAgBhL,gBAAgB,gBAEhC,MAAMiL,EAAqB/H,EAAeG,QAAQ,sBAAsBpJ,MAAWqC,KAAKgN,oBAEpF2B,IACFA,EAAmB9U,UAAU4Q,IAAIoB,IACjC8C,EAAmBnL,aAAa,eAAgB,QAEpD,CAEA+J,kBACE,MAAMxW,EAAUiJ,KAAK4M,gBAAkB5M,KAAK+N,aAE5C,IAAKhX,EACH,OAGF,MAAM6X,EAAkBjS,OAAOkS,SAAS9X,EAAQkD,aAAa,oBAAqB,IAElF+F,KAAK0F,QAAQ0G,SAAWwC,GAAmB5O,KAAK0F,QAAQuI,eAC1D,CAEAd,OAAOa,EAAOjX,EAAU,MACtB,GAAIiJ,KAAK6M,WACP,OAGF,MAAMtP,EAAgByC,KAAK+N,aACrBe,EAASd,IAAUhD,GACnB+D,EAAchY,GAAWsG,EAAqB2C,KAAK4N,YAAarQ,EAAeuR,EAAQ9O,KAAK0F,QAAQ+G,MAE1G,GAAIsC,IAAgBxR,EAClB,OAGF,MAAMyR,EAAmBhP,KAAK8N,cAAciB,GAEtCE,EAAe5I,GACZ9F,EAAasB,QAAQ7B,KAAKyF,SAAUY,EAAW,CACpDvG,cAAeiP,EACfvE,UAAWxK,KAAKkP,kBAAkBlB,GAClCvW,KAAMuI,KAAK8N,cAAcvQ,GACzBmQ,GAAIsB,IAMR,GAFmBC,EAAa7D,IAEjBnJ,iBACb,OAGF,IAAK1E,IAAkBwR,EAGrB,OAGF,MAAMI,EAAYrO,QAAQd,KAAK2M,WAC/B3M,KAAKsM,QAELtM,KAAK6M,YAAa,EAElB7M,KAAKyO,2BAA2BO,GAChChP,KAAK4M,eAAiBmC,EAEtB,MAAMK,EAAuBN,EAnSR,sBADF,oBAqSbO,EAAiBP,EAnSH,qBACA,qBAoSpBC,EAAYlV,UAAU4Q,IAAI4E,GAE1B5U,EAAOsU,GAEPxR,EAAc1D,UAAU4Q,IAAI2E,GAC5BL,EAAYlV,UAAU4Q,IAAI2E,GAa1BpP,KAAKgG,gBAXoBsJ,KACvBP,EAAYlV,UAAUlC,OAAOyX,EAAsBC,GACnDN,EAAYlV,UAAU4Q,IAAIoB,IAE1BtO,EAAc1D,UAAUlC,OAAOkU,GAAmBwD,EAAgBD,GAElEpP,KAAK6M,YAAa,EAElBoC,EAAa5D,GAAW,GAGY9N,EAAeyC,KAAKuP,eAEtDJ,GACFnP,KAAKkN,OAET,CAEAqC,cACE,OAAOvP,KAAKyF,SAAS5L,UAAUC,SAlUV,QAmUvB,CAEAiU,aACE,OAAOnH,EAAeG,QAAQiF,GAAsBhM,KAAKyF,SAC3D,CAEAmI,YACE,OAAOhH,EAAezH,KAAK4M,GAAe/L,KAAKyF,SACjD,CAEA6H,iBACMtN,KAAK2M,YACP6C,cAAcxP,KAAK2M,WACnB3M,KAAK2M,UAAY,KAErB,CAEA2B,kBAAkB9D,GAChB,OAAIzP,IACKyP,IAAcU,GAAiBD,GAAaD,GAG9CR,IAAcU,GAAiBF,GAAaC,EACrD,CAEAiE,kBAAkBlB,GAChB,OAAIjT,IACKiT,IAAU/C,GAAaC,GAAiBC,GAG1C6C,IAAU/C,GAAaE,GAAkBD,EAClD,CAGA,sBAAOzP,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAO+D,GAASvG,oBAAoBnG,KAAMwE,GAEhD,GAAsB,iBAAXA,GAKX,GAAsB,iBAAXA,EAAqB,CAC9B,QAAqBoE,IAAjBD,EAAKnE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IACP,OAVEmE,EAAK+E,GAAGlJ,EAWZ,GACF,EAOFjE,EAAac,GAAGtI,SAAU4S,GAlXE,uCAkXyC,SAAUvM,GAC7E,MAAMlC,EAAS0J,EAAekB,uBAAuB9H,MAErD,IAAK9C,IAAWA,EAAOrD,UAAUC,SAAS8R,IACxC,OAGFxM,EAAMmD,iBAEN,MAAMkN,EAAW/C,GAASvG,oBAAoBjJ,GACxCwS,EAAa1P,KAAK/F,aAAa,oBAErC,OAAIyV,GACFD,EAAS/B,GAAGgC,QACZD,EAAShC,qBAIyC,SAAhDnK,EAAYY,iBAAiBlE,KAAM,UACrCyP,EAASjI,YACTiI,EAAShC,sBAIXgC,EAASpI,YACToI,EAAShC,oBACX,IAEAlN,EAAac,GAAGrJ,OAAQ0T,IAAqB,KAC3C,MAAMiE,EAAY/I,EAAezH,KA9YR,6BAgZzB,IAAK,MAAMsQ,KAAYE,EACrBjD,GAASvG,oBAAoBsJ,EAC/B,IAOFxU,EAAmByR,ICncnB,MAEM7G,GAAY,eAGZ+J,GAAa,OAAO/J,KACpBgK,GAAc,QAAQhK,KACtBiK,GAAa,OAAOjK,KACpBkK,GAAe,SAASlK,KACxB8F,GAAuB,QAAQ9F,cAE/BmK,GAAkB,OAClBC,GAAsB,WACtBC,GAAwB,aAExBC,GAA6B,WAAWF,OAAwBA,KAOhEpH,GAAuB,8BAEvBzE,GAAU,CACdgM,OAAQ,KACRrH,QAAQ,GAGJ1E,GAAc,CAClB+L,OAAQ,iBACRrH,OAAQ,WAOV,MAAMsH,WAAiB9K,EACrBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAKsQ,kBAAmB,EACxBtQ,KAAKuQ,cAAgB,GAErB,MAAMC,EAAa5J,EAAezH,KAAK0J,IAEvC,IAAK,MAAM4H,KAAQD,EAAY,CAC7B,MAAMzY,EAAW6O,EAAeiB,uBAAuB4I,GACjDC,EAAgB9J,EAAezH,KAAKpH,GACvCgM,QAAO4M,GAAgBA,IAAiB3Q,KAAKyF,WAE/B,OAAb1N,GAAqB2Y,EAAc5X,QACrCkH,KAAKuQ,cAAczU,KAAK2U,EAE5B,CAEAzQ,KAAK4Q,sBAEA5Q,KAAK0F,QAAQ0K,QAChBpQ,KAAK6Q,0BAA0B7Q,KAAKuQ,cAAevQ,KAAK8Q,YAGtD9Q,KAAK0F,QAAQqD,QACf/I,KAAK+I,QAET,CAGA,kBAAW3E,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MA9ES,UA+EX,CAGAyN,SACM/I,KAAK8Q,WACP9Q,KAAK+Q,OAEL/Q,KAAKgR,MAET,CAEAA,OACE,GAAIhR,KAAKsQ,kBAAoBtQ,KAAK8Q,WAChC,OAGF,IAAIG,EAAiB,GASrB,GANIjR,KAAK0F,QAAQ0K,SACfa,EAAiBjR,KAAKkR,uBA9EH,wCA+EhBnN,QAAOhN,GAAWA,IAAYiJ,KAAKyF,WACnCgB,KAAI1P,GAAWsZ,GAASlK,oBAAoBpP,EAAS,CAAEgS,QAAQ,OAGhEkI,EAAenY,QAAUmY,EAAe,GAAGX,iBAC7C,OAIF,GADmB/P,EAAasB,QAAQ7B,KAAKyF,SAAUmK,IACxC3N,iBACb,OAGF,IAAK,MAAMkP,KAAkBF,EAC3BE,EAAeJ,OAGjB,MAAMK,EAAYpR,KAAKqR,gBAEvBrR,KAAKyF,SAAS5L,UAAUlC,OAAOsY,IAC/BjQ,KAAKyF,SAAS5L,UAAU4Q,IAAIyF,IAE5BlQ,KAAKyF,SAAS6L,MAAMF,GAAa,EAEjCpR,KAAK6Q,0BAA0B7Q,KAAKuQ,eAAe,GACnDvQ,KAAKsQ,kBAAmB,EAExB,MAYMiB,EAAa,SADUH,EAAU,GAAG9L,cAAgB8L,EAAUzP,MAAM,KAG1E3B,KAAKgG,gBAdYwL,KACfxR,KAAKsQ,kBAAmB,EAExBtQ,KAAKyF,SAAS5L,UAAUlC,OAAOuY,IAC/BlQ,KAAKyF,SAAS5L,UAAU4Q,IAAIwF,GAAqBD,IAEjDhQ,KAAKyF,SAAS6L,MAAMF,GAAa,GAEjC7Q,EAAasB,QAAQ7B,KAAKyF,SAAUoK,GAAY,GAMpB7P,KAAKyF,UAAU,GAC7CzF,KAAKyF,SAAS6L,MAAMF,GAAa,GAAGpR,KAAKyF,SAAS8L,MACpD,CAEAR,OACE,GAAI/Q,KAAKsQ,mBAAqBtQ,KAAK8Q,WACjC,OAIF,GADmBvQ,EAAasB,QAAQ7B,KAAKyF,SAAUqK,IACxC7N,iBACb,OAGF,MAAMmP,EAAYpR,KAAKqR,gBAEvBrR,KAAKyF,SAAS6L,MAAMF,GAAa,GAAGpR,KAAKyF,SAASgM,wBAAwBL,OAE1E3W,EAAOuF,KAAKyF,UAEZzF,KAAKyF,SAAS5L,UAAU4Q,IAAIyF,IAC5BlQ,KAAKyF,SAAS5L,UAAUlC,OAAOsY,GAAqBD,IAEpD,IAAK,MAAMnO,KAAW7B,KAAKuQ,cAAe,CACxC,MAAMxZ,EAAU6P,EAAekB,uBAAuBjG,GAElD9K,IAAYiJ,KAAK8Q,SAAS/Z,IAC5BiJ,KAAK6Q,0BAA0B,CAAChP,IAAU,EAE9C,CAEA7B,KAAKsQ,kBAAmB,EASxBtQ,KAAKyF,SAAS6L,MAAMF,GAAa,GAEjCpR,KAAKgG,gBATYwL,KACfxR,KAAKsQ,kBAAmB,EACxBtQ,KAAKyF,SAAS5L,UAAUlC,OAAOuY,IAC/BlQ,KAAKyF,SAAS5L,UAAU4Q,IAAIwF,IAC5B1P,EAAasB,QAAQ7B,KAAKyF,SAAUsK,GAAa,GAKrB/P,KAAKyF,UAAU,EAC/C,CAGAqL,SAAS/Z,EAAUiJ,KAAKyF,UACtB,OAAO1O,EAAQ8C,UAAUC,SAASkW,GACpC,CAEAtL,kBAAkBF,GAGhB,OAFAA,EAAOuE,OAASjI,QAAQ0D,EAAOuE,QAC/BvE,EAAO4L,OAASvX,EAAW2L,EAAO4L,QAC3B5L,CACT,CAEA6M,gBACE,OAAOrR,KAAKyF,SAAS5L,UAAUC,SAtLL,uBAEhB,QACC,QAoLb,CAEA8W,sBACE,IAAK5Q,KAAK0F,QAAQ0K,OAChB,OAGF,MAAMpJ,EAAWhH,KAAKkR,uBAAuBrI,IAE7C,IAAK,MAAM9R,KAAWiQ,EAAU,CAC9B,MAAM0K,EAAW9K,EAAekB,uBAAuB/Q,GAEnD2a,GACF1R,KAAK6Q,0BAA0B,CAAC9Z,GAAUiJ,KAAK8Q,SAASY,GAE5D,CACF,CAEAR,uBAAuBnZ,GACrB,MAAMiP,EAAWJ,EAAezH,KAAKgR,GAA4BnQ,KAAK0F,QAAQ0K,QAE9E,OAAOxJ,EAAezH,KAAKpH,EAAUiI,KAAK0F,QAAQ0K,QAAQrM,QAAOhN,IAAYiQ,EAAS5F,SAASrK,IACjG,CAEA8Z,0BAA0Bc,EAAcC,GACtC,GAAKD,EAAa7Y,OAIlB,IAAK,MAAM/B,KAAW4a,EACpB5a,EAAQ8C,UAAUkP,OAvNK,aAuNyB6I,GAChD7a,EAAQyM,aAAa,gBAAiBoO,EAE1C,CAGA,sBAAOnW,CAAgB+I,GACrB,MAAMkB,EAAU,GAKhB,MAJsB,iBAAXlB,GAAuB,YAAYY,KAAKZ,KACjDkB,EAAQqD,QAAS,GAGZ/I,KAAK0I,MAAK,WACf,MAAMC,EAAO0H,GAASlK,oBAAoBnG,KAAM0F,GAEhD,GAAsB,iBAAXlB,EAAqB,CAC9B,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IACP,CACF,GACF,EAOFjE,EAAac,GAAGtI,SAAU4S,GAAsB9C,IAAsB,SAAUzJ,IAEjD,MAAzBA,EAAMlC,OAAOkL,SAAoBhJ,EAAMW,gBAAmD,MAAjCX,EAAMW,eAAeqI,UAChFhJ,EAAMmD,iBAGR,IAAK,MAAMxL,KAAW6P,EAAemB,gCAAgC/H,MACnEqQ,GAASlK,oBAAoBpP,EAAS,CAAEgS,QAAQ,IAASA,QAE7D,IAMA9N,EAAmBoV,IC1QnB,MAAM/U,GAAO,WAEPuK,GAAY,eACZgF,GAAe,YAIfgH,GAAe,UACfC,GAAiB,YAGjBhC,GAAa,OAAOjK,KACpBkK,GAAe,SAASlK,KACxB+J,GAAa,OAAO/J,KACpBgK,GAAc,QAAQhK,KACtB8F,GAAuB,QAAQ9F,KAAYgF,KAC3CkH,GAAyB,UAAUlM,KAAYgF,KAC/CmH,GAAuB,QAAQnM,KAAYgF,KAE3CmF,GAAkB,OAOlBnH,GAAuB,4DACvBoJ,GAA6B,GAAGpJ,MAAwBmH,KACxDkC,GAAgB,iBAKhBC,GAAgBpX,IAAU,UAAY,YACtCqX,GAAmBrX,IAAU,YAAc,UAC3CsX,GAAmBtX,IAAU,aAAe,eAC5CuX,GAAsBvX,IAAU,eAAiB,aACjDwX,GAAkBxX,IAAU,aAAe,cAC3CyX,GAAiBzX,IAAU,cAAgB,aAI3CqJ,GAAU,CACdqO,WAAW,EACXC,SAAU,kBACVC,QAAS,UACTC,OAAQ,CAAC,EAAG,GACZC,aAAc,KACdC,UAAW,UAGPzO,GAAc,CAClBoO,UAAW,mBACXC,SAAU,mBACVC,QAAS,SACTC,OAAQ,0BACRC,aAAc,yBACdC,UAAW,2BAOb,MAAMC,WAAiBxN,EACrBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAKgT,QAAU,KACfhT,KAAKiT,QAAUjT,KAAKyF,SAAShM,WAE7BuG,KAAKkT,MAAQtM,EAAeY,KAAKxH,KAAKyF,SAAUyM,IAAe,IAC7DtL,EAAeS,KAAKrH,KAAKyF,SAAUyM,IAAe,IAClDtL,EAAeG,QAAQmL,GAAelS,KAAKiT,SAC7CjT,KAAKmT,UAAYnT,KAAKoT,eACxB,CAGA,kBAAWhP,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,OAAOA,EACT,CAGAyN,SACE,OAAO/I,KAAK8Q,WAAa9Q,KAAK+Q,OAAS/Q,KAAKgR,MAC9C,CAEAA,OACE,GAAItX,EAAWsG,KAAKyF,WAAazF,KAAK8Q,WACpC,OAGF,MAAMhR,EAAgB,CACpBA,cAAeE,KAAKyF,UAKtB,IAFkBlF,EAAasB,QAAQ7B,KAAKyF,SAAUmK,GAAY9P,GAEpDmC,iBAAd,CAUA,GANAjC,KAAKqT,gBAMD,iBAAkBta,SAASoB,kBAAoB6F,KAAKiT,QAAQ1Z,QAtFxC,eAuFtB,IAAK,MAAMxC,IAAW,GAAG8P,UAAU9N,SAAS8B,KAAKmM,UAC/CzG,EAAac,GAAGtK,EAAS,YAAayD,GAI1CwF,KAAKyF,SAAS6N,QACdtT,KAAKyF,SAASjC,aAAa,iBAAiB,GAE5CxD,KAAKkT,MAAMrZ,UAAU4Q,IAAIuF,IACzBhQ,KAAKyF,SAAS5L,UAAU4Q,IAAIuF,IAC5BzP,EAAasB,QAAQ7B,KAAKyF,SAAUoK,GAAa/P,EAnBjD,CAoBF,CAEAiR,OACE,GAAIrX,EAAWsG,KAAKyF,YAAczF,KAAK8Q,WACrC,OAGF,MAAMhR,EAAgB,CACpBA,cAAeE,KAAKyF,UAGtBzF,KAAKuT,cAAczT,EACrB,CAEA8F,UACM5F,KAAKgT,SACPhT,KAAKgT,QAAQQ,UAGfhO,MAAMI,SACR,CAEA6N,SACEzT,KAAKmT,UAAYnT,KAAKoT,gBAClBpT,KAAKgT,SACPhT,KAAKgT,QAAQS,QAEjB,CAGAF,cAAczT,GAEZ,IADkBS,EAAasB,QAAQ7B,KAAKyF,SAAUqK,GAAYhQ,GACpDmC,iBAAd,CAMA,GAAI,iBAAkBlJ,SAASoB,gBAC7B,IAAK,MAAMpD,IAAW,GAAG8P,UAAU9N,SAAS8B,KAAKmM,UAC/CzG,EAAaC,IAAIzJ,EAAS,YAAayD,GAIvCwF,KAAKgT,SACPhT,KAAKgT,QAAQQ,UAGfxT,KAAKkT,MAAMrZ,UAAUlC,OAAOqY,IAC5BhQ,KAAKyF,SAAS5L,UAAUlC,OAAOqY,IAC/BhQ,KAAKyF,SAASjC,aAAa,gBAAiB,SAC5CF,EAAYG,oBAAoBzD,KAAKkT,MAAO,UAC5C3S,EAAasB,QAAQ7B,KAAKyF,SAAUsK,GAAcjQ,GAGlDE,KAAKyF,SAAS6N,OArBd,CAsBF,CAEA/O,WAAWC,GAGT,GAAgC,iBAFhCA,EAASgB,MAAMjB,WAAWC,IAERsO,YAA2Bra,EAAU+L,EAAOsO,YACV,mBAA3CtO,EAAOsO,UAAUrB,sBAGxB,MAAM,IAAIpM,UAAU,GAAG/J,GAAKgK,+GAG9B,OAAOd,CACT,CAEA6O,gBACE,QAAsB,IAAXK,EACT,MAAM,IAAIrO,UAAU,yEAGtB,IAAIsO,EAAmB3T,KAAKyF,SAEG,WAA3BzF,KAAK0F,QAAQoN,UACfa,EAAmB3T,KAAKiT,QACfxa,EAAUuH,KAAK0F,QAAQoN,WAChCa,EAAmB9a,EAAWmH,KAAK0F,QAAQoN,WACA,iBAA3B9S,KAAK0F,QAAQoN,YAC7Ba,EAAmB3T,KAAK0F,QAAQoN,WAGlC,MAAMD,EAAe7S,KAAK4T,mBAC1B5T,KAAKgT,QAAUU,EAAOG,aAAaF,EAAkB3T,KAAKkT,MAAOL,EACnE,CAEA/B,WACE,OAAO9Q,KAAKkT,MAAMrZ,UAAUC,SAASkW,GACvC,CAEA8D,gBACE,MAAMC,EAAiB/T,KAAKiT,QAE5B,GAAIc,EAAela,UAAUC,SA5MN,WA6MrB,OAAOyY,GAGT,GAAIwB,EAAela,UAAUC,SA/MJ,aAgNvB,OAAO0Y,GAGT,GAAIuB,EAAela,UAAUC,SAlNA,iBAmN3B,MAnMsB,MAsMxB,GAAIia,EAAela,UAAUC,SArNE,mBAsN7B,MAtMyB,SA0M3B,MAAMka,EAAkF,QAA1E5a,iBAAiB4G,KAAKkT,OAAO7Z,iBAAiB,iBAAiBmN,OAE7E,OAAIuN,EAAela,UAAUC,SAhOP,UAiObka,EAAQ5B,GAAmBD,GAG7B6B,EAAQ1B,GAAsBD,EACvC,CAEAe,gBACE,OAAkD,OAA3CpT,KAAKyF,SAASlM,QA/ND,UAgOtB,CAEA0a,aACE,MAAMrB,OAAEA,GAAW5S,KAAK0F,QAExB,MAAsB,iBAAXkN,EACFA,EAAO9V,MAAM,KAAK2J,KAAI/D,GAAS/F,OAAOkS,SAASnM,EAAO,MAGzC,mBAAXkQ,EACFsB,GAActB,EAAOsB,EAAYlU,KAAKyF,UAGxCmN,CACT,CAEAgB,mBACE,MAAMO,EAAwB,CAC5BC,UAAWpU,KAAK8T,gBAChBO,UAAW,CAAC,CACVhZ,KAAM,kBACNiZ,QAAS,CACP5B,SAAU1S,KAAK0F,QAAQgN,WAG3B,CACErX,KAAM,SACNiZ,QAAS,CACP1B,OAAQ5S,KAAKiU,iBAcnB,OARIjU,KAAKmT,WAAsC,WAAzBnT,KAAK0F,QAAQiN,WACjCrP,EAAYC,iBAAiBvD,KAAKkT,MAAO,SAAU,UACnDiB,EAAsBE,UAAY,CAAC,CACjChZ,KAAM,cACNkZ,SAAS,KAIN,IACFJ,KACApY,EAAQiE,KAAK0F,QAAQmN,aAAc,MAACjK,EAAWuL,IAEtD,CAEAK,iBAAgBxd,IAAEA,EAAGkG,OAAEA,IACrB,MAAMyQ,EAAQ/G,EAAezH,KA/QF,8DA+Q+Ba,KAAKkT,OAAOnP,QAAOhN,GAAWkC,EAAUlC,KAE7F4W,EAAM7U,QAMXuE,EAAqBsQ,EAAOzQ,EAAQlG,IAAQ8a,IAAiBnE,EAAMvM,SAASlE,IAASoW,OACvF,CAGA,sBAAO7X,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOoK,GAAS5M,oBAAoBnG,KAAMwE,GAEhD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IANL,CAOF,GACF,CAEA,iBAAOiQ,CAAWrV,GAChB,GAlUuB,IAkUnBA,EAAM4J,QAAiD,UAAf5J,EAAMqB,MArUtC,QAqU0DrB,EAAMpI,IAC1E,OAGF,MAAM0d,EAAc9N,EAAezH,KAAK8S,IAExC,IAAK,MAAMlJ,KAAU2L,EAAa,CAChC,MAAMC,EAAU5B,GAAS7M,YAAY6C,GACrC,IAAK4L,IAAyC,IAA9BA,EAAQjP,QAAQ+M,UAC9B,SAGF,MAAMmC,EAAexV,EAAMwV,eACrBC,EAAeD,EAAaxT,SAASuT,EAAQzB,OACnD,GACE0B,EAAaxT,SAASuT,EAAQlP,WACC,WAA9BkP,EAAQjP,QAAQ+M,YAA2BoC,GACb,YAA9BF,EAAQjP,QAAQ+M,WAA2BoC,EAE5C,SAIF,GAAIF,EAAQzB,MAAMpZ,SAASsF,EAAMlC,UAA4B,UAAfkC,EAAMqB,MA5V1C,QA4V8DrB,EAAMpI,KAAoB,qCAAqCoO,KAAKhG,EAAMlC,OAAOkL,UACvJ,SAGF,MAAMtI,EAAgB,CAAEA,cAAe6U,EAAQlP,UAE5B,UAAfrG,EAAMqB,OACRX,EAAcqI,WAAa/I,GAG7BuV,EAAQpB,cAAczT,EACxB,CACF,CAEA,4BAAOgV,CAAsB1V,GAI3B,MAAM2V,EAAU,kBAAkB3P,KAAKhG,EAAMlC,OAAOkL,SAC9C4M,EAhXS,WAgXO5V,EAAMpI,IACtBie,EAAkB,CAACpD,GAAcC,IAAgB1Q,SAAShC,EAAMpI,KAEtE,IAAKie,IAAoBD,EACvB,OAGF,GAAID,IAAYC,EACd,OAGF5V,EAAMmD,iBAGN,MAAM2S,EAAkBlV,KAAKkH,QAAQ2B,IACnC7I,KACC4G,EAAeS,KAAKrH,KAAM6I,IAAsB,IAC/CjC,EAAeY,KAAKxH,KAAM6I,IAAsB,IAChDjC,EAAeG,QAAQ8B,GAAsBzJ,EAAMW,eAAetG,YAEhExC,EAAW8b,GAAS5M,oBAAoB+O,GAE9C,GAAID,EAIF,OAHA7V,EAAM+V,kBACNle,EAAS+Z,YACT/Z,EAASud,gBAAgBpV,GAIvBnI,EAAS6Z,aACX1R,EAAM+V,kBACNle,EAAS8Z,OACTmE,EAAgB5B,QAEpB,EAOF/S,EAAac,GAAGtI,SAAUgZ,GAAwBlJ,GAAsBkK,GAAS+B,uBACjFvU,EAAac,GAAGtI,SAAUgZ,GAAwBG,GAAea,GAAS+B,uBAC1EvU,EAAac,GAAGtI,SAAU4S,GAAsBoH,GAAS0B,YACzDlU,EAAac,GAAGtI,SAAUiZ,GAAsBe,GAAS0B,YACzDlU,EAAac,GAAGtI,SAAU4S,GAAsB9C,IAAsB,SAAUzJ,GAC9EA,EAAMmD,iBACNwQ,GAAS5M,oBAAoBnG,MAAM+I,QACrC,IAMA9N,EAAmB8X,ICtbnB,MAAMzX,GAAO,WAEP0U,GAAkB,OAClBoF,GAAkB,gBAAgB9Z,KAElC8I,GAAU,CACdiR,UAAW,iBACXC,cAAe,KACfrP,YAAY,EACZhN,WAAW,EACXsc,YAAa,QAGTlR,GAAc,CAClBgR,UAAW,SACXC,cAAe,kBACfrP,WAAY,UACZhN,UAAW,UACXsc,YAAa,oBAOf,MAAMC,WAAiBrR,EACrBU,YAAYL,GACVgB,QACAxF,KAAK0F,QAAU1F,KAAKuE,WAAWC,GAC/BxE,KAAKyV,aAAc,EACnBzV,KAAKyF,SAAW,IAClB,CAGA,kBAAWrB,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,OAAOA,EACT,CAGA0V,KAAK7V,GACH,IAAK6E,KAAK0F,QAAQzM,UAEhB,YADA8C,EAAQZ,GAIV6E,KAAK0V,UAEL,MAAM3e,EAAUiJ,KAAK2V,cACjB3V,KAAK0F,QAAQO,YACfxL,EAAO1D,GAGTA,EAAQ8C,UAAU4Q,IAAIuF,IAEtBhQ,KAAK4V,mBAAkB,KACrB7Z,EAAQZ,EAAS,GAErB,CAEA4V,KAAK5V,GACE6E,KAAK0F,QAAQzM,WAKlB+G,KAAK2V,cAAc9b,UAAUlC,OAAOqY,IAEpChQ,KAAK4V,mBAAkB,KACrB5V,KAAK4F,UACL7J,EAAQZ,EAAS,KARjBY,EAAQZ,EAUZ,CAEAyK,UACO5F,KAAKyV,cAIVlV,EAAaC,IAAIR,KAAKyF,SAAU2P,IAEhCpV,KAAKyF,SAAS9N,SACdqI,KAAKyV,aAAc,EACrB,CAGAE,cACE,IAAK3V,KAAKyF,SAAU,CAClB,MAAMoQ,EAAW9c,SAAS+c,cAAc,OACxCD,EAASR,UAAYrV,KAAK0F,QAAQ2P,UAC9BrV,KAAK0F,QAAQO,YACf4P,EAAShc,UAAU4Q,IAjGH,QAoGlBzK,KAAKyF,SAAWoQ,CAClB,CAEA,OAAO7V,KAAKyF,QACd,CAEAf,kBAAkBF,GAGhB,OADAA,EAAO+Q,YAAc1c,EAAW2L,EAAO+Q,aAChC/Q,CACT,CAEAkR,UACE,GAAI1V,KAAKyV,YACP,OAGF,MAAM1e,EAAUiJ,KAAK2V,cACrB3V,KAAK0F,QAAQ6P,YAAYQ,OAAOhf,GAEhCwJ,EAAac,GAAGtK,EAASqe,IAAiB,KACxCrZ,EAAQiE,KAAK0F,QAAQ4P,cAAc,IAGrCtV,KAAKyV,aAAc,CACrB,CAEAG,kBAAkBza,GAChBiB,EAAuBjB,EAAU6E,KAAK2V,cAAe3V,KAAK0F,QAAQO,WACpE,ECpIF,MAEMJ,GAAY,gBACZmQ,GAAgB,UAAUnQ,KAC1BoQ,GAAoB,cAAcpQ,KAIlCqQ,GAAmB,WAEnB9R,GAAU,CACd+R,WAAW,EACXC,YAAa,MAGT/R,GAAc,CAClB8R,UAAW,UACXC,YAAa,WAOf,MAAMC,WAAkBlS,EACtBU,YAAYL,GACVgB,QACAxF,KAAK0F,QAAU1F,KAAKuE,WAAWC,GAC/BxE,KAAKsW,WAAY,EACjBtW,KAAKuW,qBAAuB,IAC9B,CAGA,kBAAWnS,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MA1CS,WA2CX,CAGAkb,WACMxW,KAAKsW,YAILtW,KAAK0F,QAAQyQ,WACfnW,KAAK0F,QAAQ0Q,YAAY9C,QAG3B/S,EAAaC,IAAIzH,SAAU8M,IAC3BtF,EAAac,GAAGtI,SAAUid,IAAe5W,GAASY,KAAKyW,eAAerX,KACtEmB,EAAac,GAAGtI,SAAUkd,IAAmB7W,GAASY,KAAK0W,eAAetX,KAE1EY,KAAKsW,WAAY,EACnB,CAEAK,aACO3W,KAAKsW,YAIVtW,KAAKsW,WAAY,EACjB/V,EAAaC,IAAIzH,SAAU8M,IAC7B,CAGA4Q,eAAerX,GACb,MAAMgX,YAAEA,GAAgBpW,KAAK0F,QAE7B,GAAItG,EAAMlC,SAAWnE,UAAYqG,EAAMlC,SAAWkZ,GAAeA,EAAYtc,SAASsF,EAAMlC,QAC1F,OAGF,MAAM0Z,EAAWhQ,EAAec,kBAAkB0O,GAE1B,IAApBQ,EAAS9d,OACXsd,EAAY9C,QACHtT,KAAKuW,uBAAyBL,GACvCU,EAASA,EAAS9d,OAAS,GAAGwa,QAE9BsD,EAAS,GAAGtD,OAEhB,CAEAoD,eAAetX,GApFD,QAqFRA,EAAMpI,MAIVgJ,KAAKuW,qBAAuBnX,EAAMyX,SAAWX,GAxFzB,UAyFtB,EChGF,MAAMY,GAAyB,oDACzBC,GAA0B,cAC1BC,GAAmB,gBACnBC,GAAkB,eAMxB,MAAMC,GACJrS,cACE7E,KAAKyF,SAAW1M,SAAS8B,IAC3B,CAGAsc,WAEE,MAAMC,EAAgBre,SAASoB,gBAAgBkd,YAC/C,OAAOxZ,KAAK0M,IAAIvS,OAAOsf,WAAaF,EACtC,CAEArG,OACE,MAAMwG,EAAQvX,KAAKmX,WACnBnX,KAAKwX,mBAELxX,KAAKyX,sBAAsBzX,KAAKyF,SAAUuR,IAAkBU,GAAmBA,EAAkBH,IAEjGvX,KAAKyX,sBAAsBX,GAAwBE,IAAkBU,GAAmBA,EAAkBH,IAC1GvX,KAAKyX,sBAAsBV,GAAyBE,IAAiBS,GAAmBA,EAAkBH,GAC5G,CAEAI,QACE3X,KAAK4X,wBAAwB5X,KAAKyF,SAAU,YAC5CzF,KAAK4X,wBAAwB5X,KAAKyF,SAAUuR,IAC5ChX,KAAK4X,wBAAwBd,GAAwBE,IACrDhX,KAAK4X,wBAAwBb,GAAyBE,GACxD,CAEAY,gBACE,OAAO7X,KAAKmX,WAAa,CAC3B,CAGAK,mBACExX,KAAK8X,sBAAsB9X,KAAKyF,SAAU,YAC1CzF,KAAKyF,SAAS6L,MAAMyG,SAAW,QACjC,CAEAN,sBAAsB1f,EAAUigB,EAAe7c,GAC7C,MAAM8c,EAAiBjY,KAAKmX,WAW5BnX,KAAKkY,2BAA2BngB,GAVHhB,IAC3B,GAAIA,IAAYiJ,KAAKyF,UAAYzN,OAAOsf,WAAavgB,EAAQsgB,YAAcY,EACzE,OAGFjY,KAAK8X,sBAAsB/gB,EAASihB,GACpC,MAAMN,EAAkB1f,OAAOoB,iBAAiBrC,GAASsC,iBAAiB2e,GAC1EjhB,EAAQua,MAAM6G,YAAYH,EAAe,GAAG7c,EAASwB,OAAOC,WAAW8a,QAAsB,GAIjG,CAEAI,sBAAsB/gB,EAASihB,GAC7B,MAAMI,EAAcrhB,EAAQua,MAAMjY,iBAAiB2e,GAC/CI,GACF9U,EAAYC,iBAAiBxM,EAASihB,EAAeI,EAEzD,CAEAR,wBAAwB7f,EAAUigB,GAahChY,KAAKkY,2BAA2BngB,GAZHhB,IAC3B,MAAM2L,EAAQY,EAAYY,iBAAiBnN,EAASihB,GAEtC,OAAVtV,GAKJY,EAAYG,oBAAoB1M,EAASihB,GACzCjhB,EAAQua,MAAM6G,YAAYH,EAAetV,IALvC3L,EAAQua,MAAM+G,eAAeL,EAKgB,GAInD,CAEAE,2BAA2BngB,EAAUugB,GACnC,GAAI7f,EAAUV,GACZugB,EAASvgB,QAIX,IAAK,MAAM2O,KAAOE,EAAezH,KAAKpH,EAAUiI,KAAKyF,UACnD6S,EAAS5R,EAEb,ECxFF,MAEMb,GAAY,YAIZiK,GAAa,OAAOjK,KACpB0S,GAAuB,gBAAgB1S,KACvCkK,GAAe,SAASlK,KACxB+J,GAAa,OAAO/J,KACpBgK,GAAc,QAAQhK,KACtB2S,GAAe,SAAS3S,KACxB4S,GAAsB,gBAAgB5S,KACtC6S,GAA0B,oBAAoB7S,KAC9C8S,GAAwB,kBAAkB9S,KAC1C8F,GAAuB,QAAQ9F,cAE/B+S,GAAkB,aAElB5I,GAAkB,OAClB6I,GAAoB,eAOpBzU,GAAU,CACdyR,UAAU,EACVvC,OAAO,EACPjH,UAAU,GAGNhI,GAAc,CAClBwR,SAAU,mBACVvC,MAAO,UACPjH,SAAU,WAOZ,MAAMyM,WAAcvT,EAClBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAK+Y,QAAUnS,EAAeG,QAxBV,gBAwBmC/G,KAAKyF,UAC5DzF,KAAKgZ,UAAYhZ,KAAKiZ,sBACtBjZ,KAAKkZ,WAAalZ,KAAKmZ,uBACvBnZ,KAAK8Q,UAAW,EAChB9Q,KAAKsQ,kBAAmB,EACxBtQ,KAAKoZ,WAAa,IAAIlC,GAEtBlX,KAAKiN,oBACP,CAGA,kBAAW7I,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MAnES,OAoEX,CAGAyN,OAAOjJ,GACL,OAAOE,KAAK8Q,SAAW9Q,KAAK+Q,OAAS/Q,KAAKgR,KAAKlR,EACjD,CAEAkR,KAAKlR,GACCE,KAAK8Q,UAAY9Q,KAAKsQ,kBAIR/P,EAAasB,QAAQ7B,KAAKyF,SAAUmK,GAAY,CAChE9P,kBAGYmC,mBAIdjC,KAAK8Q,UAAW,EAChB9Q,KAAKsQ,kBAAmB,EAExBtQ,KAAKoZ,WAAWrI,OAEhBhY,SAAS8B,KAAKhB,UAAU4Q,IAAImO,IAE5B5Y,KAAKqZ,gBAELrZ,KAAKgZ,UAAUhI,MAAK,IAAMhR,KAAKsZ,aAAaxZ,KAC9C,CAEAiR,OACO/Q,KAAK8Q,WAAY9Q,KAAKsQ,mBAIT/P,EAAasB,QAAQ7B,KAAKyF,SAAUqK,IAExC7N,mBAIdjC,KAAK8Q,UAAW,EAChB9Q,KAAKsQ,kBAAmB,EACxBtQ,KAAKkZ,WAAWvC,aAEhB3W,KAAKyF,SAAS5L,UAAUlC,OAAOqY,IAE/BhQ,KAAKgG,gBAAe,IAAMhG,KAAKuZ,cAAcvZ,KAAKyF,SAAUzF,KAAKuP,gBACnE,CAEA3J,UACErF,EAAaC,IAAIxI,OAAQ6N,IACzBtF,EAAaC,IAAIR,KAAK+Y,QAASlT,IAE/B7F,KAAKgZ,UAAUpT,UACf5F,KAAKkZ,WAAWvC,aAEhBnR,MAAMI,SACR,CAEA4T,eACExZ,KAAKqZ,eACP,CAGAJ,sBACE,OAAO,IAAIzD,GAAS,CAClBvc,UAAW6H,QAAQd,KAAK0F,QAAQmQ,UAChC5P,WAAYjG,KAAKuP,eAErB,CAEA4J,uBACE,OAAO,IAAI9C,GAAU,CACnBD,YAAapW,KAAKyF,UAEtB,CAEA6T,aAAaxZ,GAEN/G,SAAS8B,KAAKf,SAASkG,KAAKyF,WAC/B1M,SAAS8B,KAAKkb,OAAO/V,KAAKyF,UAG5BzF,KAAKyF,SAAS6L,MAAMqB,QAAU,QAC9B3S,KAAKyF,SAAS/B,gBAAgB,eAC9B1D,KAAKyF,SAASjC,aAAa,cAAc,GACzCxD,KAAKyF,SAASjC,aAAa,OAAQ,UACnCxD,KAAKyF,SAASgU,UAAY,EAE1B,MAAMC,EAAY9S,EAAeG,QAxIT,cAwIsC/G,KAAK+Y,SAC/DW,IACFA,EAAUD,UAAY,GAGxBhf,EAAOuF,KAAKyF,UAEZzF,KAAKyF,SAAS5L,UAAU4Q,IAAIuF,IAa5BhQ,KAAKgG,gBAXsB2T,KACrB3Z,KAAK0F,QAAQ4N,OACftT,KAAKkZ,WAAW1C,WAGlBxW,KAAKsQ,kBAAmB,EACxB/P,EAAasB,QAAQ7B,KAAKyF,SAAUoK,GAAa,CAC/C/P,iBACA,GAGoCE,KAAK+Y,QAAS/Y,KAAKuP,cAC7D,CAEAtC,qBACE1M,EAAac,GAAGrB,KAAKyF,SAAUkT,IAAuBvZ,IApLvC,WAqLTA,EAAMpI,MAINgJ,KAAK0F,QAAQ2G,SACfrM,KAAK+Q,OAIP/Q,KAAK4Z,6BAA4B,IAGnCrZ,EAAac,GAAGrJ,OAAQwgB,IAAc,KAChCxY,KAAK8Q,WAAa9Q,KAAKsQ,kBACzBtQ,KAAKqZ,eACP,IAGF9Y,EAAac,GAAGrB,KAAKyF,SAAUiT,IAAyBtZ,IAEtDmB,EAAae,IAAItB,KAAKyF,SAAUgT,IAAqBoB,IAC/C7Z,KAAKyF,WAAarG,EAAMlC,QAAU8C,KAAKyF,WAAaoU,EAAO3c,SAIjC,WAA1B8C,KAAK0F,QAAQmQ,SAKb7V,KAAK0F,QAAQmQ,UACf7V,KAAK+Q,OALL/Q,KAAK4Z,6BAMP,GACA,GAEN,CAEAL,aACEvZ,KAAKyF,SAAS6L,MAAMqB,QAAU,OAC9B3S,KAAKyF,SAASjC,aAAa,eAAe,GAC1CxD,KAAKyF,SAAS/B,gBAAgB,cAC9B1D,KAAKyF,SAAS/B,gBAAgB,QAC9B1D,KAAKsQ,kBAAmB,EAExBtQ,KAAKgZ,UAAUjI,MAAK,KAClBhY,SAAS8B,KAAKhB,UAAUlC,OAAOihB,IAC/B5Y,KAAK8Z,oBACL9Z,KAAKoZ,WAAWzB,QAChBpX,EAAasB,QAAQ7B,KAAKyF,SAAUsK,GAAa,GAErD,CAEAR,cACE,OAAOvP,KAAKyF,SAAS5L,UAAUC,SA5NX,OA6NtB,CAEA8f,6BAEE,GADkBrZ,EAAasB,QAAQ7B,KAAKyF,SAAU8S,IACxCtW,iBACZ,OAGF,MAAM8X,EAAqB/Z,KAAKyF,SAASuU,aAAejhB,SAASoB,gBAAgB8f,aAC3EC,EAAmBla,KAAKyF,SAAS6L,MAAM6I,UAEpB,WAArBD,GAAiCla,KAAKyF,SAAS5L,UAAUC,SAAS+e,MAIjEkB,IACH/Z,KAAKyF,SAAS6L,MAAM6I,UAAY,UAGlCna,KAAKyF,SAAS5L,UAAU4Q,IAAIoO,IAC5B7Y,KAAKgG,gBAAe,KAClBhG,KAAKyF,SAAS5L,UAAUlC,OAAOkhB,IAC/B7Y,KAAKgG,gBAAe,KAClBhG,KAAKyF,SAAS6L,MAAM6I,UAAYD,CAAgB,GAC/Cla,KAAK+Y,QAAQ,GACf/Y,KAAK+Y,SAER/Y,KAAKyF,SAAS6N,QAChB,CAMA+F,gBACE,MAAMU,EAAqB/Z,KAAKyF,SAASuU,aAAejhB,SAASoB,gBAAgB8f,aAC3EhC,EAAiBjY,KAAKoZ,WAAWjC,WACjCiD,EAAoBnC,EAAiB,EAE3C,GAAImC,IAAsBL,EAAoB,CAC5C,MAAMhV,EAAWhK,IAAU,cAAgB,eAC3CiF,KAAKyF,SAAS6L,MAAMvM,GAAY,GAAGkT,KACrC,CAEA,IAAKmC,GAAqBL,EAAoB,CAC5C,MAAMhV,EAAWhK,IAAU,eAAiB,cAC5CiF,KAAKyF,SAAS6L,MAAMvM,GAAY,GAAGkT,KACrC,CACF,CAEA6B,oBACE9Z,KAAKyF,SAAS6L,MAAM+I,YAAc,GAClCra,KAAKyF,SAAS6L,MAAMgJ,aAAe,EACrC,CAGA,sBAAO7e,CAAgB+I,EAAQ1E,GAC7B,OAAOE,KAAK0I,MAAK,WACf,MAAMC,EAAOmQ,GAAM3S,oBAAoBnG,KAAMwE,GAE7C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,GAAQ1E,EANb,CAOF,GACF,EAOFS,EAAac,GAAGtI,SAAU4S,GAnSG,4BAmSyC,SAAUvM,GAC9E,MAAMlC,EAAS0J,EAAekB,uBAAuB9H,MAEjD,CAAC,IAAK,QAAQoB,SAASpB,KAAKoI,UAC9BhJ,EAAMmD,iBAGRhC,EAAae,IAAIpE,EAAQ0S,IAAY2K,IAC/BA,EAAUtY,kBAKd1B,EAAae,IAAIpE,EAAQ6S,IAAc,KACjC9W,EAAU+G,OACZA,KAAKsT,OACP,GACA,IAIJ,MAAMkH,EAAc5T,EAAeG,QA3Tf,eA4ThByT,GACF1B,GAAM5S,YAAYsU,GAAazJ,OAGpB+H,GAAM3S,oBAAoBjJ,GAElC6L,OAAO/I,KACd,IAEAgI,EAAqB8Q,IAMrB7d,EAAmB6d,IC/VnB,MAEMjT,GAAY,gBACZgF,GAAe,YACfa,GAAsB,OAAO7F,KAAYgF,KAGzCmF,GAAkB,OAClByK,GAAqB,UACrBC,GAAoB,SAEpBC,GAAgB,kBAEhB/K,GAAa,OAAO/J,KACpBgK,GAAc,QAAQhK,KACtBiK,GAAa,OAAOjK,KACpB0S,GAAuB,gBAAgB1S,KACvCkK,GAAe,SAASlK,KACxB2S,GAAe,SAAS3S,KACxB8F,GAAuB,QAAQ9F,KAAYgF,KAC3C8N,GAAwB,kBAAkB9S,KAI1CzB,GAAU,CACdyR,UAAU,EACVxJ,UAAU,EACVuO,QAAQ,GAGJvW,GAAc,CAClBwR,SAAU,mBACVxJ,SAAU,UACVuO,OAAQ,WAOV,MAAMC,WAAkBtV,EACtBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAK8Q,UAAW,EAChB9Q,KAAKgZ,UAAYhZ,KAAKiZ,sBACtBjZ,KAAKkZ,WAAalZ,KAAKmZ,uBACvBnZ,KAAKiN,oBACP,CAGA,kBAAW7I,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MA5DS,WA6DX,CAGAyN,OAAOjJ,GACL,OAAOE,KAAK8Q,SAAW9Q,KAAK+Q,OAAS/Q,KAAKgR,KAAKlR,EACjD,CAEAkR,KAAKlR,GACCE,KAAK8Q,UAISvQ,EAAasB,QAAQ7B,KAAKyF,SAAUmK,GAAY,CAAE9P,kBAEtDmC,mBAIdjC,KAAK8Q,UAAW,EAChB9Q,KAAKgZ,UAAUhI,OAEVhR,KAAK0F,QAAQkV,SAChB,IAAI1D,IAAkBnG,OAGxB/Q,KAAKyF,SAASjC,aAAa,cAAc,GACzCxD,KAAKyF,SAASjC,aAAa,OAAQ,UACnCxD,KAAKyF,SAAS5L,UAAU4Q,IAAIgQ,IAY5Bza,KAAKgG,gBAVoBsJ,KAClBtP,KAAK0F,QAAQkV,SAAU5a,KAAK0F,QAAQmQ,UACvC7V,KAAKkZ,WAAW1C,WAGlBxW,KAAKyF,SAAS5L,UAAU4Q,IAAIuF,IAC5BhQ,KAAKyF,SAAS5L,UAAUlC,OAAO8iB,IAC/Bla,EAAasB,QAAQ7B,KAAKyF,SAAUoK,GAAa,CAAE/P,iBAAgB,GAG/BE,KAAKyF,UAAU,GACvD,CAEAsL,OACO/Q,KAAK8Q,WAIQvQ,EAAasB,QAAQ7B,KAAKyF,SAAUqK,IAExC7N,mBAIdjC,KAAKkZ,WAAWvC,aAChB3W,KAAKyF,SAASqV,OACd9a,KAAK8Q,UAAW,EAChB9Q,KAAKyF,SAAS5L,UAAU4Q,IAAIiQ,IAC5B1a,KAAKgZ,UAAUjI,OAcf/Q,KAAKgG,gBAZoB+U,KACvB/a,KAAKyF,SAAS5L,UAAUlC,OAAOqY,GAAiB0K,IAChD1a,KAAKyF,SAAS/B,gBAAgB,cAC9B1D,KAAKyF,SAAS/B,gBAAgB,QAEzB1D,KAAK0F,QAAQkV,SAChB,IAAI1D,IAAkBS,QAGxBpX,EAAasB,QAAQ7B,KAAKyF,SAAUsK,GAAa,GAGb/P,KAAKyF,UAAU,IACvD,CAEAG,UACE5F,KAAKgZ,UAAUpT,UACf5F,KAAKkZ,WAAWvC,aAChBnR,MAAMI,SACR,CAGAqT,sBACE,MAUMhgB,EAAY6H,QAAQd,KAAK0F,QAAQmQ,UAEvC,OAAO,IAAIL,GAAS,CAClBH,UAlJsB,qBAmJtBpc,YACAgN,YAAY,EACZsP,YAAavV,KAAKyF,SAAShM,WAC3B6b,cAAerc,EAjBKqc,KACU,WAA1BtV,KAAK0F,QAAQmQ,SAKjB7V,KAAK+Q,OAJHxQ,EAAasB,QAAQ7B,KAAKyF,SAAU8S,GAI3B,EAWgC,MAE/C,CAEAY,uBACE,OAAO,IAAI9C,GAAU,CACnBD,YAAapW,KAAKyF,UAEtB,CAEAwH,qBACE1M,EAAac,GAAGrB,KAAKyF,SAAUkT,IAAuBvZ,IAtKvC,WAuKTA,EAAMpI,MAINgJ,KAAK0F,QAAQ2G,SACfrM,KAAK+Q,OAIPxQ,EAAasB,QAAQ7B,KAAKyF,SAAU8S,IAAqB,GAE7D,CAGA,sBAAO9c,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOkS,GAAU1U,oBAAoBnG,KAAMwE,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqBoE,IAAjBD,EAAKnE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,GAAQxE,KANb,CAOF,GACF,EAOFO,EAAac,GAAGtI,SAAU4S,GAzLG,gCAyLyC,SAAUvM,GAC9E,MAAMlC,EAAS0J,EAAekB,uBAAuB9H,MAMrD,GAJI,CAAC,IAAK,QAAQoB,SAASpB,KAAKoI,UAC9BhJ,EAAMmD,iBAGJ7I,EAAWsG,MACb,OAGFO,EAAae,IAAIpE,EAAQ6S,IAAc,KAEjC9W,EAAU+G,OACZA,KAAKsT,OACP,IAIF,MAAMkH,EAAc5T,EAAeG,QAAQ4T,IACvCH,GAAeA,IAAgBtd,GACjC2d,GAAU3U,YAAYsU,GAAazJ,OAGxB8J,GAAU1U,oBAAoBjJ,GACtC6L,OAAO/I,KACd,IAEAO,EAAac,GAAGrJ,OAAQ0T,IAAqB,KAC3C,IAAK,MAAM3T,KAAY6O,EAAezH,KAAKwb,IACzCE,GAAU1U,oBAAoBpO,GAAUiZ,MAC1C,IAGFzQ,EAAac,GAAGrJ,OAAQwgB,IAAc,KACpC,IAAK,MAAMzhB,KAAW6P,EAAezH,KAAK,gDACG,UAAvC/F,iBAAiBrC,GAASikB,UAC5BH,GAAU1U,oBAAoBpP,GAASga,MAE3C,IAGF/I,EAAqB6S,IAMrB5f,EAAmB4f,IC/QnB,MAEaI,GAAmB,CAE9B,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAJP,kBAK7BC,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,GAAI,GACJC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHhO,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDiO,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAIAC,GAAgB,IAAIxe,IAAI,CAC5B,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUIye,GAAmB,0DAEnBC,GAAmBA,CAACC,EAAWC,KACnC,MAAMC,EAAgBF,EAAUG,SAASla,cAEzC,OAAIga,EAAqBjc,SAASkc,IAC5BL,GAAc/lB,IAAIomB,IACbxc,QAAQoc,GAAiB9X,KAAKgY,EAAUI,YAO5CH,EAAqBtZ,QAAO0Z,GAAkBA,aAA0BtY,SAC5EuY,MAAKC,GAASA,EAAMvY,KAAKkY,IAAe,EC/DvClZ,GAAU,CACdwZ,UAAW3C,GACX4C,QAAS,GACTC,WAAY,GACZC,MAAM,EACNC,UAAU,EACVC,WAAY,KACZC,SAAU,eAGN7Z,GAAc,CAClBuZ,UAAW,SACXC,QAAS,SACTC,WAAY,oBACZC,KAAM,UACNC,SAAU,UACVC,WAAY,kBACZC,SAAU,UAGNC,GAAqB,CACzBC,MAAO,iCACPrmB,SAAU,oBAOZ,MAAMsmB,WAAwBla,EAC5BU,YAAYL,GACVgB,QACAxF,KAAK0F,QAAU1F,KAAKuE,WAAWC,EACjC,CAGA,kBAAWJ,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MA/CS,iBAgDX,CAGAgjB,aACE,OAAOrf,OAAOC,OAAOc,KAAK0F,QAAQmY,SAC/BpX,KAAIjC,GAAUxE,KAAKue,yBAAyB/Z,KAC5CT,OAAOjD,QACZ,CAEA0d,aACE,OAAOxe,KAAKse,aAAaxlB,OAAS,CACpC,CAEA2lB,cAAcZ,GAGZ,OAFA7d,KAAK0e,cAAcb,GACnB7d,KAAK0F,QAAQmY,QAAU,IAAK7d,KAAK0F,QAAQmY,WAAYA,GAC9C7d,IACT,CAEA2e,SACE,MAAMC,EAAkB7lB,SAAS+c,cAAc,OAC/C8I,EAAgBC,UAAY7e,KAAK8e,eAAe9e,KAAK0F,QAAQwY,UAE7D,IAAK,MAAOnmB,EAAUgnB,KAAS9f,OAAOkC,QAAQnB,KAAK0F,QAAQmY,SACzD7d,KAAKgf,YAAYJ,EAAiBG,EAAMhnB,GAG1C,MAAMmmB,EAAWU,EAAgB5X,SAAS,GACpC8W,EAAa9d,KAAKue,yBAAyBve,KAAK0F,QAAQoY,YAM9D,OAJIA,GACFI,EAASrkB,UAAU4Q,OAAOqT,EAAWhhB,MAAM,MAGtCohB,CACT,CAGAvZ,iBAAiBH,GACfgB,MAAMb,iBAAiBH,GACvBxE,KAAK0e,cAAcla,EAAOqZ,QAC5B,CAEAa,cAAcO,GACZ,IAAK,MAAOlnB,EAAU8lB,KAAY5e,OAAOkC,QAAQ8d,GAC/CzZ,MAAMb,iBAAiB,CAAE5M,WAAUqmB,MAAOP,GAAWM,GAEzD,CAEAa,YAAYd,EAAUL,EAAS9lB,GAC7B,MAAMmnB,EAAkBtY,EAAeG,QAAQhP,EAAUmmB,GAEpDgB,KAILrB,EAAU7d,KAAKue,yBAAyBV,IAOpCplB,EAAUolB,GACZ7d,KAAKmf,sBAAsBtmB,EAAWglB,GAAUqB,GAI9Clf,KAAK0F,QAAQqY,KACfmB,EAAgBL,UAAY7e,KAAK8e,eAAejB,GAIlDqB,EAAgBE,YAAcvB,EAd5BqB,EAAgBvnB,SAepB,CAEAmnB,eAAeG,GACb,OAAOjf,KAAK0F,QAAQsY,SDzDjB,SAAsBqB,EAAYzB,EAAW0B,GAClD,IAAKD,EAAWvmB,OACd,OAAOumB,EAGT,GAAIC,GAAgD,mBAArBA,EAC7B,OAAOA,EAAiBD,GAG1B,MACME,GADY,IAAIvnB,OAAOwnB,WACKC,gBAAgBJ,EAAY,aACxDzI,EAAW,GAAG/P,UAAU0Y,EAAgB1kB,KAAKuF,iBAAiB,MAEpE,IAAK,MAAMrJ,KAAW6f,EAAU,CAC9B,MAAM8I,EAAc3oB,EAAQwmB,SAASla,cAErC,IAAKpE,OAAOvH,KAAKkmB,GAAWxc,SAASse,GAAc,CACjD3oB,EAAQY,SACR,QACF,CAEA,MAAMgoB,EAAgB,GAAG9Y,UAAU9P,EAAQ6M,YACrCgc,EAAoB,GAAG/Y,OAAO+W,EAAU,MAAQ,GAAIA,EAAU8B,IAAgB,IAEpF,IAAK,MAAMtC,KAAauC,EACjBxC,GAAiBC,EAAWwC,IAC/B7oB,EAAQ2M,gBAAgB0Z,EAAUG,SAGxC,CAEA,OAAOgC,EAAgB1kB,KAAKgkB,SAC9B,CCyBmCgB,CAAaZ,EAAKjf,KAAK0F,QAAQkY,UAAW5d,KAAK0F,QAAQuY,YAAcgB,CACtG,CAEAV,yBAAyBU,GACvB,OAAOljB,EAAQkjB,EAAK,MAACrW,EAAW5I,MAClC,CAEAmf,sBAAsBpoB,EAASmoB,GAC7B,GAAIlf,KAAK0F,QAAQqY,KAGf,OAFAmB,EAAgBL,UAAY,QAC5BK,EAAgBnJ,OAAOhf,GAIzBmoB,EAAgBE,YAAcroB,EAAQqoB,WACxC,ECvIF,MACMU,GAAwB,IAAIrhB,IAAI,CAAC,WAAY,YAAa,eAE1DshB,GAAkB,OAElB/P,GAAkB,OAElBgQ,GAAyB,iBACzBC,GAAiB,SAEjBC,GAAmB,gBAEnBC,GAAgB,QAChBC,GAAgB,QAehBC,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOzlB,IAAU,OAAS,QAC1B0lB,OAAQ,SACRC,KAAM3lB,IAAU,QAAU,QAGtBqJ,GAAU,CACdwZ,UAAW3C,GACX0F,WAAW,EACXjO,SAAU,kBACVkO,WAAW,EACXC,YAAa,GACbC,MAAO,EACPC,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/ChD,MAAM,EACNnL,OAAQ,CAAC,EAAG,GACZwB,UAAW,MACXvB,aAAc,KACdmL,UAAU,EACVC,WAAY,KACZlmB,UAAU,EACVmmB,SAAU,+GAIV8C,MAAO,GACPnf,QAAS,eAGLwC,GAAc,CAClBuZ,UAAW,SACX+C,UAAW,UACXjO,SAAU,mBACVkO,UAAW,2BACXC,YAAa,oBACbC,MAAO,kBACPC,mBAAoB,QACpBhD,KAAM,UACNnL,OAAQ,0BACRwB,UAAW,oBACXvB,aAAc,yBACdmL,SAAU,UACVC,WAAY,kBACZlmB,SAAU,mBACVmmB,SAAU,SACV8C,MAAO,4BACPnf,QAAS,UAOX,MAAMof,WAAgB1b,EACpBV,YAAY9N,EAASyN,GACnB,QAAsB,IAAXkP,EACT,MAAM,IAAIrO,UAAU,wEAGtBG,MAAMzO,EAASyN,GAGfxE,KAAKkhB,YAAa,EAClBlhB,KAAKmhB,SAAW,EAChBnhB,KAAKohB,WAAa,KAClBphB,KAAKqhB,eAAiB,GACtBrhB,KAAKgT,QAAU,KACfhT,KAAKshB,iBAAmB,KACxBthB,KAAKuhB,YAAc,KAGnBvhB,KAAKwhB,IAAM,KAEXxhB,KAAKyhB,gBAEAzhB,KAAK0F,QAAQ3N,UAChBiI,KAAK0hB,WAET,CAGA,kBAAWtd,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MAxHS,SAyHX,CAGAqmB,SACE3hB,KAAKkhB,YAAa,CACpB,CAEAU,UACE5hB,KAAKkhB,YAAa,CACpB,CAEAW,gBACE7hB,KAAKkhB,YAAclhB,KAAKkhB,UAC1B,CAEAnY,SACO/I,KAAKkhB,aAINlhB,KAAK8Q,WACP9Q,KAAK8hB,SAIP9hB,KAAK+hB,SACP,CAEAnc,UACE4I,aAAaxO,KAAKmhB,UAElB5gB,EAAaC,IAAIR,KAAKyF,SAASlM,QAAQ0mB,IAAiBC,GAAkBlgB,KAAKgiB,mBAE3EhiB,KAAKyF,SAASxL,aAAa,2BAC7B+F,KAAKyF,SAASjC,aAAa,QAASxD,KAAKyF,SAASxL,aAAa,2BAGjE+F,KAAKiiB,iBACLzc,MAAMI,SACR,CAEAoL,OACE,GAAoC,SAAhChR,KAAKyF,SAAS6L,MAAMqB,QACtB,MAAM,IAAIrO,MAAM,uCAGlB,IAAMtE,KAAKkiB,mBAAoBliB,KAAKkhB,WAClC,OAGF,MAAM3G,EAAYha,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYwB,UAxJxD,SA0JT8b,GADajoB,EAAe8F,KAAKyF,WACLzF,KAAKyF,SAAS2c,cAAcjoB,iBAAiBL,SAASkG,KAAKyF,UAE7F,GAAI8U,EAAUtY,mBAAqBkgB,EACjC,OAIFniB,KAAKiiB,iBAEL,MAAMT,EAAMxhB,KAAKqiB,iBAEjBriB,KAAKyF,SAASjC,aAAa,mBAAoBge,EAAIvnB,aAAa,OAEhE,MAAM2mB,UAAEA,GAAc5gB,KAAK0F,QAe3B,GAbK1F,KAAKyF,SAAS2c,cAAcjoB,gBAAgBL,SAASkG,KAAKwhB,OAC7DZ,EAAU7K,OAAOyL,GACjBjhB,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYwB,UAzKpC,cA4KnBrG,KAAKgT,QAAUhT,KAAKqT,cAAcmO,GAElCA,EAAI3nB,UAAU4Q,IAAIuF,IAMd,iBAAkBjX,SAASoB,gBAC7B,IAAK,MAAMpD,IAAW,GAAG8P,UAAU9N,SAAS8B,KAAKmM,UAC/CzG,EAAac,GAAGtK,EAAS,YAAayD,GAc1CwF,KAAKgG,gBAVYwL,KACfjR,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYwB,UA5LvC,WA8LU,IAApBrG,KAAKohB,YACPphB,KAAK8hB,SAGP9hB,KAAKohB,YAAa,CAAK,GAGKphB,KAAKwhB,IAAKxhB,KAAKuP,cAC/C,CAEAwB,OACE,GAAK/Q,KAAK8Q,aAIQvQ,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYwB,UAhNxD,SAiNDpE,iBAAd,CASA,GALYjC,KAAKqiB,iBACbxoB,UAAUlC,OAAOqY,IAIjB,iBAAkBjX,SAASoB,gBAC7B,IAAK,MAAMpD,IAAW,GAAG8P,UAAU9N,SAAS8B,KAAKmM,UAC/CzG,EAAaC,IAAIzJ,EAAS,YAAayD,GAI3CwF,KAAKqhB,eAA4B,OAAI,EACrCrhB,KAAKqhB,eAAejB,KAAiB,EACrCpgB,KAAKqhB,eAAelB,KAAiB,EACrCngB,KAAKohB,WAAa,KAelBphB,KAAKgG,gBAbYwL,KACXxR,KAAKsiB,yBAIJtiB,KAAKohB,YACRphB,KAAKiiB,iBAGPjiB,KAAKyF,SAAS/B,gBAAgB,oBAC9BnD,EAAasB,QAAQ7B,KAAKyF,SAAUzF,KAAK6E,YAAYwB,UA9OtC,WA8O8D,GAGjDrG,KAAKwhB,IAAKxhB,KAAKuP,cA/B7C,CAgCF,CAEAkE,SACMzT,KAAKgT,SACPhT,KAAKgT,QAAQS,QAEjB,CAGAyO,iBACE,OAAOphB,QAAQd,KAAKuiB,YACtB,CAEAF,iBAKE,OAJKriB,KAAKwhB,MACRxhB,KAAKwhB,IAAMxhB,KAAKwiB,kBAAkBxiB,KAAKuhB,aAAevhB,KAAKyiB,2BAGtDziB,KAAKwhB,GACd,CAEAgB,kBAAkB3E,GAChB,MAAM2D,EAAMxhB,KAAK0iB,oBAAoB7E,GAASc,SAG9C,IAAK6C,EACH,OAAO,KAGTA,EAAI3nB,UAAUlC,OAAOooB,GAAiB/P,IAEtCwR,EAAI3nB,UAAU4Q,IAAI,MAAMzK,KAAK6E,YAAYvJ,aAEzC,MAAMqnB,EpBpRKC,KACb,GACEA,GAAU/kB,KAAKglB,MAjCH,IAiCShlB,KAAKilB,gBACnB/pB,SAASgqB,eAAeH,IAEjC,OAAOA,CAAM,EoB+QGI,CAAOhjB,KAAK6E,YAAYvJ,MAAMyH,WAQ5C,OANAye,EAAIhe,aAAa,KAAMmf,GAEnB3iB,KAAKuP,eACPiS,EAAI3nB,UAAU4Q,IAAIsV,IAGbyB,CACT,CAEAyB,WAAWpF,GACT7d,KAAKuhB,YAAc1D,EACf7d,KAAK8Q,aACP9Q,KAAKiiB,iBACLjiB,KAAKgR,OAET,CAEA0R,oBAAoB7E,GAalB,OAZI7d,KAAKshB,iBACPthB,KAAKshB,iBAAiB7C,cAAcZ,GAEpC7d,KAAKshB,iBAAmB,IAAIjD,GAAgB,IACvCre,KAAK0F,QAGRmY,UACAC,WAAY9d,KAAKue,yBAAyBve,KAAK0F,QAAQmb,eAIpD7gB,KAAKshB,gBACd,CAEAmB,yBACE,MAAO,CACLzC,CAACA,IAAyBhgB,KAAKuiB,YAEnC,CAEAA,YACE,OAAOviB,KAAKue,yBAAyBve,KAAK0F,QAAQsb,QAAUhhB,KAAKyF,SAASxL,aAAa,yBACzF,CAGAipB,6BAA6B9jB,GAC3B,OAAOY,KAAK6E,YAAYsB,oBAAoB/G,EAAMW,eAAgBC,KAAKmjB,qBACzE,CAEA5T,cACE,OAAOvP,KAAK0F,QAAQib,WAAc3gB,KAAKwhB,KAAOxhB,KAAKwhB,IAAI3nB,UAAUC,SAASimB,GAC5E,CAEAjP,WACE,OAAO9Q,KAAKwhB,KAAOxhB,KAAKwhB,IAAI3nB,UAAUC,SAASkW,GACjD,CAEAqD,cAAcmO,GACZ,MAAMpN,EAAYrY,EAAQiE,KAAK0F,QAAQ0O,UAAW,CAACpU,KAAMwhB,EAAKxhB,KAAKyF,WAC7D2d,EAAa/C,GAAcjM,EAAU9O,eAC3C,OAAOoO,EAAOG,aAAa7T,KAAKyF,SAAU+b,EAAKxhB,KAAK4T,iBAAiBwP,GACvE,CAEAnP,aACE,MAAMrB,OAAEA,GAAW5S,KAAK0F,QAExB,MAAsB,iBAAXkN,EACFA,EAAO9V,MAAM,KAAK2J,KAAI/D,GAAS/F,OAAOkS,SAASnM,EAAO,MAGzC,mBAAXkQ,EACFsB,GAActB,EAAOsB,EAAYlU,KAAKyF,UAGxCmN,CACT,CAEA2L,yBAAyBU,GACvB,OAAOljB,EAAQkjB,EAAK,CAACjf,KAAKyF,SAAUzF,KAAKyF,UAC3C,CAEAmO,iBAAiBwP,GACf,MAAMjP,EAAwB,CAC5BC,UAAWgP,EACX/O,UAAW,CACT,CACEhZ,KAAM,OACNiZ,QAAS,CACPyM,mBAAoB/gB,KAAK0F,QAAQqb,qBAGrC,CACE1lB,KAAM,SACNiZ,QAAS,CACP1B,OAAQ5S,KAAKiU,eAGjB,CACE5Y,KAAM,kBACNiZ,QAAS,CACP5B,SAAU1S,KAAK0F,QAAQgN,WAG3B,CACErX,KAAM,QACNiZ,QAAS,CACPvd,QAAS,IAAIiJ,KAAK6E,YAAYvJ,eAGlC,CACED,KAAM,kBACNkZ,SAAS,EACT8O,MAAO,aACP7nB,GAAImN,IAGF3I,KAAKqiB,iBAAiB7e,aAAa,wBAAyBmF,EAAK2a,MAAMlP,UAAU,KAMzF,MAAO,IACFD,KACApY,EAAQiE,KAAK0F,QAAQmN,aAAc,MAACjK,EAAWuL,IAEtD,CAEAsN,gBACE,MAAM8B,EAAWvjB,KAAK0F,QAAQ7D,QAAQ/E,MAAM,KAE5C,IAAK,MAAM+E,KAAW0hB,EACpB,GAAgB,UAAZ1hB,EACFtB,EAAac,GAAGrB,KAAKyF,SAAUzF,KAAK6E,YAAYwB,UArZpC,SAqZ4DrG,KAAK0F,QAAQ3N,UAAUqH,IAC7EY,KAAKkjB,6BAA6B9jB,GAC1C2J,QAAQ,SAEb,GAhaU,WAgaNlH,EAA4B,CACrC,MAAM2hB,EAAU3hB,IAAYse,GAC1BngB,KAAK6E,YAAYwB,UAxZF,cAyZfrG,KAAK6E,YAAYwB,UA3ZL,WA4ZRod,EAAW5hB,IAAYse,GAC3BngB,KAAK6E,YAAYwB,UA1ZF,cA2ZfrG,KAAK6E,YAAYwB,UA7ZJ,YA+Zf9F,EAAac,GAAGrB,KAAKyF,SAAU+d,EAASxjB,KAAK0F,QAAQ3N,UAAUqH,IAC7D,MAAMuV,EAAU3U,KAAKkjB,6BAA6B9jB,GAClDuV,EAAQ0M,eAA8B,YAAfjiB,EAAMqB,KAAqB2f,GAAgBD,KAAiB,EACnFxL,EAAQoN,QAAQ,IAElBxhB,EAAac,GAAGrB,KAAKyF,SAAUge,EAAUzjB,KAAK0F,QAAQ3N,UAAUqH,IAC9D,MAAMuV,EAAU3U,KAAKkjB,6BAA6B9jB,GAClDuV,EAAQ0M,eAA8B,aAAfjiB,EAAMqB,KAAsB2f,GAAgBD,IACjExL,EAAQlP,SAAS3L,SAASsF,EAAMU,eAElC6U,EAAQmN,QAAQ,GAEpB,CAGF9hB,KAAKgiB,kBAAoB,KACnBhiB,KAAKyF,UACPzF,KAAK+Q,MACP,EAGFxQ,EAAac,GAAGrB,KAAKyF,SAASlM,QAAQ0mB,IAAiBC,GAAkBlgB,KAAKgiB,kBAChF,CAEAN,YACE,MAAMV,EAAQhhB,KAAKyF,SAASxL,aAAa,SAEpC+mB,IAIAhhB,KAAKyF,SAASxL,aAAa,eAAkB+F,KAAKyF,SAAS2Z,YAAY5Y,QAC1ExG,KAAKyF,SAASjC,aAAa,aAAcwd,GAG3ChhB,KAAKyF,SAASjC,aAAa,yBAA0Bwd,GACrDhhB,KAAKyF,SAAS/B,gBAAgB,SAChC,CAEAqe,SACM/hB,KAAK8Q,YAAc9Q,KAAKohB,WAC1BphB,KAAKohB,YAAa,GAIpBphB,KAAKohB,YAAa,EAElBphB,KAAK0jB,aAAY,KACX1jB,KAAKohB,YACPphB,KAAKgR,MACP,GACChR,KAAK0F,QAAQob,MAAM9P,MACxB,CAEA8Q,SACM9hB,KAAKsiB,yBAITtiB,KAAKohB,YAAa,EAElBphB,KAAK0jB,aAAY,KACV1jB,KAAKohB,YACRphB,KAAK+Q,MACP,GACC/Q,KAAK0F,QAAQob,MAAM/P,MACxB,CAEA2S,YAAYzmB,EAAS0mB,GACnBnV,aAAaxO,KAAKmhB,UAClBnhB,KAAKmhB,SAAW/jB,WAAWH,EAAS0mB,EACtC,CAEArB,uBACE,OAAOrjB,OAAOC,OAAOc,KAAKqhB,gBAAgBjgB,UAAS,EACrD,CAEAmD,WAAWC,GACT,MAAMof,EAAiBtgB,EAAYK,kBAAkB3D,KAAKyF,UAE1D,IAAK,MAAMoe,KAAiB5kB,OAAOvH,KAAKksB,GAClC9D,GAAsB5oB,IAAI2sB,WACrBD,EAAeC,GAW1B,OAPArf,EAAS,IACJof,KACmB,iBAAXpf,GAAuBA,EAASA,EAAS,IAEtDA,EAASxE,KAAKyE,gBAAgBD,GAC9BA,EAASxE,KAAK0E,kBAAkBF,GAChCxE,KAAK2E,iBAAiBH,GACfA,CACT,CAEAE,kBAAkBF,GAkBhB,OAjBAA,EAAOoc,WAAiC,IAArBpc,EAAOoc,UAAsB7nB,SAAS8B,KAAOhC,EAAW2L,EAAOoc,WAEtD,iBAAjBpc,EAAOsc,QAChBtc,EAAOsc,MAAQ,CACb9P,KAAMxM,EAAOsc,MACb/P,KAAMvM,EAAOsc,QAIW,iBAAjBtc,EAAOwc,QAChBxc,EAAOwc,MAAQxc,EAAOwc,MAAMje,YAGA,iBAAnByB,EAAOqZ,UAChBrZ,EAAOqZ,QAAUrZ,EAAOqZ,QAAQ9a,YAG3ByB,CACT,CAEA2e,qBACE,MAAM3e,EAAS,GAEf,IAAK,MAAOxN,EAAK0L,KAAUzD,OAAOkC,QAAQnB,KAAK0F,SACzC1F,KAAK6E,YAAYT,QAAQpN,KAAS0L,IACpC8B,EAAOxN,GAAO0L,GAUlB,OANA8B,EAAOzM,UAAW,EAClByM,EAAO3C,QAAU,SAKV2C,CACT,CAEAyd,iBACMjiB,KAAKgT,UACPhT,KAAKgT,QAAQQ,UACbxT,KAAKgT,QAAU,MAGbhT,KAAKwhB,MACPxhB,KAAKwhB,IAAI7pB,SACTqI,KAAKwhB,IAAM,KAEf,CAGA,sBAAO/lB,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOsY,GAAQ9a,oBAAoBnG,KAAMwE,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IANL,CAOF,GACF,EAOFvJ,EAAmBgmB,ICvmBnB,MAEM6C,GAAiB,kBACjBC,GAAmB,gBAEnB3f,GAAU,IACX6c,GAAQ7c,QACXyZ,QAAS,GACTjL,OAAQ,CAAC,EAAG,GACZwB,UAAW,QACX8J,SAAU,8IAKVrc,QAAS,SAGLwC,GAAc,IACf4c,GAAQ5c,YACXwZ,QAAS,kCAOX,MAAMmG,WAAgB/C,GAEpB,kBAAW7c,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MAtCS,SAuCX,CAGA4mB,iBACE,OAAOliB,KAAKuiB,aAAeviB,KAAKikB,aAClC,CAGAxB,yBACE,MAAO,CACLqB,CAACA,IAAiB9jB,KAAKuiB,YACvBwB,CAACA,IAAmB/jB,KAAKikB,cAE7B,CAEAA,cACE,OAAOjkB,KAAKue,yBAAyBve,KAAK0F,QAAQmY,QACpD,CAGA,sBAAOpiB,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOqb,GAAQ7d,oBAAoBnG,KAAMwE,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IANL,CAOF,GACF,EAOFvJ,EAAmB+oB,IC5EnB,MAEMne,GAAY,gBAGZqe,GAAiB,WAAWre,KAC5Bse,GAAc,QAAQte,KACtB6F,GAAsB,OAAO7F,cAG7BgG,GAAoB,SAGpBuY,GAAwB,SAExBC,GAAqB,YAGrBC,GAAsB,GAAGD,mBAA+CA,uBAIxEjgB,GAAU,CACdwO,OAAQ,KACR2R,WAAY,eACZC,cAAc,EACdtnB,OAAQ,KACRunB,UAAW,CAAC,GAAK,GAAK,IAGlBpgB,GAAc,CAClBuO,OAAQ,gBACR2R,WAAY,SACZC,aAAc,UACdtnB,OAAQ,UACRunB,UAAW,SAOb,MAAMC,WAAkBnf,EACtBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAGfxE,KAAK2kB,aAAe,IAAI/tB,IACxBoJ,KAAK4kB,oBAAsB,IAAIhuB,IAC/BoJ,KAAK6kB,aAA6D,YAA9CzrB,iBAAiB4G,KAAKyF,UAAU0U,UAA0B,KAAOna,KAAKyF,SAC1FzF,KAAK8kB,cAAgB,KACrB9kB,KAAK+kB,UAAY,KACjB/kB,KAAKglB,oBAAsB,CACzBC,gBAAiB,EACjBC,gBAAiB,GAEnBllB,KAAKmlB,SACP,CAGA,kBAAW/gB,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MArES,WAsEX,CAGA6pB,UACEnlB,KAAKolB,mCACLplB,KAAKqlB,2BAEDrlB,KAAK+kB,UACP/kB,KAAK+kB,UAAUO,aAEftlB,KAAK+kB,UAAY/kB,KAAKulB,kBAGxB,IAAK,MAAMC,KAAWxlB,KAAK4kB,oBAAoB1lB,SAC7Cc,KAAK+kB,UAAUU,QAAQD,EAE3B,CAEA5f,UACE5F,KAAK+kB,UAAUO,aACf9f,MAAMI,SACR,CAGAlB,kBAAkBF,GAWhB,OATAA,EAAOtH,OAASrE,EAAW2L,EAAOtH,SAAWnE,SAAS8B,KAGtD2J,EAAO+f,WAAa/f,EAAOoO,OAAS,GAAGpO,EAAOoO,oBAAsBpO,EAAO+f,WAE3C,iBAArB/f,EAAOigB,YAChBjgB,EAAOigB,UAAYjgB,EAAOigB,UAAU3nB,MAAM,KAAK2J,KAAI/D,GAAS/F,OAAOC,WAAW8F,MAGzE8B,CACT,CAEA6gB,2BACOrlB,KAAK0F,QAAQ8e,eAKlBjkB,EAAaC,IAAIR,KAAK0F,QAAQxI,OAAQinB,IAEtC5jB,EAAac,GAAGrB,KAAK0F,QAAQxI,OAAQinB,GAAaC,IAAuBhlB,IACvE,MAAMsmB,EAAoB1lB,KAAK4kB,oBAAoBxtB,IAAIgI,EAAMlC,OAAOyoB,MACpE,GAAID,EAAmB,CACrBtmB,EAAMmD,iBACN,MAAMjI,EAAO0F,KAAK6kB,cAAgB7sB,OAC5B4tB,EAASF,EAAkBG,UAAY7lB,KAAKyF,SAASogB,UAC3D,GAAIvrB,EAAKwrB,SAEP,YADAxrB,EAAKwrB,SAAS,CAAEC,IAAKH,EAAQI,SAAU,WAKzC1rB,EAAKmf,UAAYmM,CACnB,KAEJ,CAEAL,kBACE,MAAMjR,EAAU,CACdha,KAAM0F,KAAK6kB,aACXJ,UAAWzkB,KAAK0F,QAAQ+e,UACxBF,WAAYvkB,KAAK0F,QAAQ6e,YAG3B,OAAO,IAAI0B,sBAAqB9kB,GAAWnB,KAAKkmB,kBAAkB/kB,IAAUmT,EAC9E,CAGA4R,kBAAkB/kB,GAChB,MAAMglB,EAAgB/H,GAASpe,KAAK2kB,aAAavtB,IAAI,IAAIgnB,EAAMlhB,OAAO7E,MAChEme,EAAW4H,IACfpe,KAAKglB,oBAAoBC,gBAAkB7G,EAAMlhB,OAAO2oB,UACxD7lB,KAAKomB,SAASD,EAAc/H,GAAO,EAG/B8G,GAAmBllB,KAAK6kB,cAAgB9rB,SAASoB,iBAAiBsf,UAClE4M,EAAkBnB,GAAmBllB,KAAKglB,oBAAoBE,gBACpEllB,KAAKglB,oBAAoBE,gBAAkBA,EAE3C,IAAK,MAAM9G,KAASjd,EAAS,CAC3B,IAAKid,EAAMkI,eAAgB,CACzBtmB,KAAK8kB,cAAgB,KACrB9kB,KAAKumB,kBAAkBJ,EAAc/H,IAErC,QACF,CAEA,MAAMoI,EAA2BpI,EAAMlhB,OAAO2oB,WAAa7lB,KAAKglB,oBAAoBC,gBAEpF,GAAIoB,GAAmBG,GAGrB,GAFAhQ,EAAS4H,IAEJ8G,EACH,YAOCmB,GAAoBG,GACvBhQ,EAAS4H,EAEb,CACF,CAEAgH,mCACEplB,KAAK2kB,aAAe,IAAI/tB,IACxBoJ,KAAK4kB,oBAAsB,IAAIhuB,IAE/B,MAAM6vB,EAAc7f,EAAezH,KAAKilB,GAAuBpkB,KAAK0F,QAAQxI,QAE5E,IAAK,MAAMwpB,KAAUD,EAAa,CAEhC,IAAKC,EAAOf,MAAQjsB,EAAWgtB,GAC7B,SAGF,MAAMhB,EAAoB9e,EAAeG,QAAQ4f,UAAUD,EAAOf,MAAO3lB,KAAKyF,UAG1ExM,EAAUysB,KACZ1lB,KAAK2kB,aAAa7tB,IAAI6vB,UAAUD,EAAOf,MAAOe,GAC9C1mB,KAAK4kB,oBAAoB9tB,IAAI4vB,EAAOf,KAAMD,GAE9C,CACF,CAEAU,SAASlpB,GACH8C,KAAK8kB,gBAAkB5nB,IAI3B8C,KAAKumB,kBAAkBvmB,KAAK0F,QAAQxI,QACpC8C,KAAK8kB,cAAgB5nB,EACrBA,EAAOrD,UAAU4Q,IAAIoB,IACrB7L,KAAK4mB,iBAAiB1pB,GAEtBqD,EAAasB,QAAQ7B,KAAKyF,SAAUye,GAAgB,CAAEpkB,cAAe5C,IACvE,CAEA0pB,iBAAiB1pB,GAEf,GAAIA,EAAOrD,UAAUC,SAlNQ,iBAmN3B8M,EAAeG,QAxMY,mBAwMsB7J,EAAO3D,QAzMpC,cA0MjBM,UAAU4Q,IAAIoB,SAInB,IAAK,MAAMgb,KAAajgB,EAAeO,QAAQjK,EAnNnB,qBAsN1B,IAAK,MAAM4pB,KAAQlgB,EAAeS,KAAKwf,EAAWvC,IAChDwC,EAAKjtB,UAAU4Q,IAAIoB,GAGzB,CAEA0a,kBAAkBnW,GAChBA,EAAOvW,UAAUlC,OAAOkU,IAExB,MAAMkb,EAAcngB,EAAezH,KAAK,GAAGilB,MAAyBvY,KAAqBuE,GACzF,IAAK,MAAM4W,KAAQD,EACjBC,EAAKntB,UAAUlC,OAAOkU,GAE1B,CAGA,sBAAOpQ,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAO+b,GAAUve,oBAAoBnG,KAAMwE,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqBoE,IAAjBD,EAAKnE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IANL,CAOF,GACF,EAOFjE,EAAac,GAAGrJ,OAAQ0T,IAAqB,KAC3C,IAAK,MAAMub,KAAOrgB,EAAezH,KA9PT,0BA+PtBulB,GAAUve,oBAAoB8gB,EAChC,IAOFhsB,EAAmBypB,ICrRnB,MAEM7e,GAAY,UAEZiK,GAAa,OAAOjK,KACpBkK,GAAe,SAASlK,KACxB+J,GAAa,OAAO/J,KACpBgK,GAAc,QAAQhK,KACtB8F,GAAuB,QAAQ9F,KAC/ByF,GAAgB,UAAUzF,KAC1B6F,GAAsB,OAAO7F,KAE7BiF,GAAiB,YACjBC,GAAkB,aAClB8G,GAAe,UACfC,GAAiB,YACjBoV,GAAW,OACXC,GAAU,MAEVtb,GAAoB,SACpBkU,GAAkB,OAClB/P,GAAkB,OAGlBoX,GAA2B,mBAE3BC,GAA+B,QAAQD,MAKvCve,GAAuB,2EACvBye,GAAsB,YAFOD,uBAAiDA,mBAA6CA,OAE/Exe,KAE5C0e,GAA8B,IAAI1b,8BAA6CA,+BAA8CA,4BAMnI,MAAM2b,WAAYjiB,EAChBV,YAAY9N,GACVyO,MAAMzO,GACNiJ,KAAKiT,QAAUjT,KAAKyF,SAASlM,QAfN,uCAiBlByG,KAAKiT,UAOVjT,KAAKynB,sBAAsBznB,KAAKiT,QAASjT,KAAK0nB,gBAE9CnnB,EAAac,GAAGrB,KAAKyF,SAAU6F,IAAelM,GAASY,KAAKkO,SAAS9O,KACvE,CAGA,eAAW9D,GACT,MA3DS,KA4DX,CAGA0V,OACE,MAAM2W,EAAY3nB,KAAKyF,SACvB,GAAIzF,KAAK4nB,cAAcD,GACrB,OAIF,MAAME,EAAS7nB,KAAK8nB,iBAEdC,EAAYF,EAChBtnB,EAAasB,QAAQgmB,EAAQ/X,GAAY,CAAEhQ,cAAe6nB,IAC1D,KAEgBpnB,EAAasB,QAAQ8lB,EAAW/X,GAAY,CAAE9P,cAAe+nB,IAEjE5lB,kBAAqB8lB,GAAaA,EAAU9lB,mBAI1DjC,KAAKgoB,YAAYH,EAAQF,GACzB3nB,KAAKioB,UAAUN,EAAWE,GAC5B,CAGAI,UAAUlxB,EAASmxB,GACZnxB,IAILA,EAAQ8C,UAAU4Q,IAAIoB,IAEtB7L,KAAKioB,UAAUrhB,EAAekB,uBAAuB/Q,IAgBrDiJ,KAAKgG,gBAdYwL,KACsB,QAAjCza,EAAQkD,aAAa,SAKzBlD,EAAQ2M,gBAAgB,YACxB3M,EAAQyM,aAAa,iBAAiB,GACtCxD,KAAKmoB,gBAAgBpxB,GAAS,GAC9BwJ,EAAasB,QAAQ9K,EAAS8Y,GAAa,CACzC/P,cAAeooB,KARfnxB,EAAQ8C,UAAU4Q,IAAIuF,GAStB,GAG0BjZ,EAASA,EAAQ8C,UAAUC,SAASimB,KACpE,CAEAiI,YAAYjxB,EAASmxB,GACdnxB,IAILA,EAAQ8C,UAAUlC,OAAOkU,IACzB9U,EAAQ+jB,OAER9a,KAAKgoB,YAAYphB,EAAekB,uBAAuB/Q,IAcvDiJ,KAAKgG,gBAZYwL,KACsB,QAAjCza,EAAQkD,aAAa,SAKzBlD,EAAQyM,aAAa,iBAAiB,GACtCzM,EAAQyM,aAAa,WAAY,MACjCxD,KAAKmoB,gBAAgBpxB,GAAS,GAC9BwJ,EAAasB,QAAQ9K,EAASgZ,GAAc,CAAEjQ,cAAeooB,KAP3DnxB,EAAQ8C,UAAUlC,OAAOqY,GAOgD,GAG/CjZ,EAASA,EAAQ8C,UAAUC,SAASimB,KACpE,CAEA7R,SAAS9O,GACP,IAAM,CAAC0L,GAAgBC,GAAiB8G,GAAcC,GAAgBoV,GAAUC,IAAS/lB,SAAShC,EAAMpI,KACtG,OAGFoI,EAAM+V,kBACN/V,EAAMmD,iBAEN,MAAMyE,EAAWhH,KAAK0nB,eAAe3jB,QAAOhN,IAAY2C,EAAW3C,KACnE,IAAIqxB,EAEJ,GAAI,CAAClB,GAAUC,IAAS/lB,SAAShC,EAAMpI,KACrCoxB,EAAoBphB,EAAS5H,EAAMpI,MAAQkwB,GAAW,EAAIlgB,EAASlO,OAAS,OACvE,CACL,MAAMgW,EAAS,CAAC/D,GAAiB+G,IAAgB1Q,SAAShC,EAAMpI,KAChEoxB,EAAoB/qB,EAAqB2J,EAAU5H,EAAMlC,OAAQ4R,GAAQ,EAC3E,CAEIsZ,IACFA,EAAkB9U,MAAM,CAAE+U,eAAe,IACzCb,GAAIrhB,oBAAoBiiB,GAAmBpX,OAE/C,CAEA0W,eACE,OAAO9gB,EAAezH,KAAKmoB,GAAqBtnB,KAAKiT,QACvD,CAEA6U,iBACE,OAAO9nB,KAAK0nB,eAAevoB,MAAK8H,GAASjH,KAAK4nB,cAAc3gB,MAAW,IACzE,CAEAwgB,sBAAsBrX,EAAQpJ,GAC5BhH,KAAKsoB,yBAAyBlY,EAAQ,OAAQ,WAE9C,IAAK,MAAMnJ,KAASD,EAClBhH,KAAKuoB,6BAA6BthB,EAEtC,CAEAshB,6BAA6BthB,GAC3BA,EAAQjH,KAAKwoB,iBAAiBvhB,GAC9B,MAAMwhB,EAAWzoB,KAAK4nB,cAAc3gB,GAC9ByhB,EAAY1oB,KAAK2oB,iBAAiB1hB,GACxCA,EAAMzD,aAAa,gBAAiBilB,GAEhCC,IAAczhB,GAChBjH,KAAKsoB,yBAAyBI,EAAW,OAAQ,gBAG9CD,GACHxhB,EAAMzD,aAAa,WAAY,MAGjCxD,KAAKsoB,yBAAyBrhB,EAAO,OAAQ,OAG7CjH,KAAK4oB,mCAAmC3hB,EAC1C,CAEA2hB,mCAAmC3hB,GACjC,MAAM/J,EAAS0J,EAAekB,uBAAuBb,GAEhD/J,IAIL8C,KAAKsoB,yBAAyBprB,EAAQ,OAAQ,YAE1C+J,EAAM5O,IACR2H,KAAKsoB,yBAAyBprB,EAAQ,kBAAmB,GAAG+J,EAAM5O,MAEtE,CAEA8vB,gBAAgBpxB,EAAS8xB,GACvB,MAAMH,EAAY1oB,KAAK2oB,iBAAiB5xB,GACxC,IAAK2xB,EAAU7uB,UAAUC,SAhMN,YAiMjB,OAGF,MAAMiP,EAASA,CAAChR,EAAUsd,KACxB,MAAMte,EAAU6P,EAAeG,QAAQhP,EAAU2wB,GAC7C3xB,GACFA,EAAQ8C,UAAUkP,OAAOsM,EAAWwT,EACtC,EAGF9f,EAAOqe,GAA0Bvb,IACjC9C,EAzM2B,iBAyMIiH,IAC/B0Y,EAAUllB,aAAa,gBAAiBqlB,EAC1C,CAEAP,yBAAyBvxB,EAASqmB,EAAW1a,GACtC3L,EAAQiD,aAAaojB,IACxBrmB,EAAQyM,aAAa4Z,EAAW1a,EAEpC,CAEAklB,cAAcnX,GACZ,OAAOA,EAAK5W,UAAUC,SAAS+R,GACjC,CAGA2c,iBAAiB/X,GACf,OAAOA,EAAKvJ,QAAQogB,IAAuB7W,EAAO7J,EAAeG,QAAQugB,GAAqB7W,EAChG,CAGAkY,iBAAiBlY,GACf,OAAOA,EAAKlX,QA1NO,gCA0NoBkX,CACzC,CAGA,sBAAOhV,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAO6e,GAAIrhB,oBAAoBnG,MAErC,GAAsB,iBAAXwE,EAAX,CAIA,QAAqBoE,IAAjBD,EAAKnE,IAAyBA,EAAO/C,WAAW,MAAmB,gBAAX+C,EAC1D,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,IANL,CAOF,GACF,EAOFjE,EAAac,GAAGtI,SAAU4S,GAAsB9C,IAAsB,SAAUzJ,GAC1E,CAAC,IAAK,QAAQgC,SAASpB,KAAKoI,UAC9BhJ,EAAMmD,iBAGJ7I,EAAWsG,OAIfwnB,GAAIrhB,oBAAoBnG,MAAMgR,MAChC,IAKAzQ,EAAac,GAAGrJ,OAAQ0T,IAAqB,KAC3C,IAAK,MAAM3U,KAAW6P,EAAezH,KAAKooB,IACxCC,GAAIrhB,oBAAoBpP,EAC1B,IAMFkE,EAAmBusB,ICxSnB,MAEM3hB,GAAY,YAEZijB,GAAkB,YAAYjjB,KAC9BkjB,GAAiB,WAAWljB,KAC5BmQ,GAAgB,UAAUnQ,KAC1BmjB,GAAiB,WAAWnjB,KAC5BiK,GAAa,OAAOjK,KACpBkK,GAAe,SAASlK,KACxB+J,GAAa,OAAO/J,KACpBgK,GAAc,QAAQhK,KAGtBojB,GAAkB,OAClBjZ,GAAkB,OAClByK,GAAqB,UAErBpW,GAAc,CAClBsc,UAAW,UACXuI,SAAU,UACVpI,MAAO,UAGH1c,GAAU,CACduc,WAAW,EACXuI,UAAU,EACVpI,MAAO,KAOT,MAAMqI,WAAc5jB,EAClBV,YAAY9N,EAASyN,GACnBgB,MAAMzO,EAASyN,GAEfxE,KAAKmhB,SAAW,KAChBnhB,KAAKopB,sBAAuB,EAC5BppB,KAAKqpB,yBAA0B,EAC/BrpB,KAAKyhB,eACP,CAGA,kBAAWrd,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW/I,GACT,MAtDS,OAuDX,CAGA0V,OACoBzQ,EAAasB,QAAQ7B,KAAKyF,SAAUmK,IAExC3N,mBAIdjC,KAAKspB,gBAEDtpB,KAAK0F,QAAQib,WACf3gB,KAAKyF,SAAS5L,UAAU4Q,IAvDN,QAiEpBzK,KAAKyF,SAAS5L,UAAUlC,OAAOsxB,IAC/BxuB,EAAOuF,KAAKyF,UACZzF,KAAKyF,SAAS5L,UAAU4Q,IAAIuF,GAAiByK,IAE7Cza,KAAKgG,gBAXYwL,KACfxR,KAAKyF,SAAS5L,UAAUlC,OAAO8iB,IAC/Bla,EAAasB,QAAQ7B,KAAKyF,SAAUoK,IAEpC7P,KAAKupB,oBAAoB,GAOGvpB,KAAKyF,SAAUzF,KAAK0F,QAAQib,WAC5D,CAEA5P,OACO/Q,KAAKwpB,YAIQjpB,EAAasB,QAAQ7B,KAAKyF,SAAUqK,IAExC7N,mBAUdjC,KAAKyF,SAAS5L,UAAU4Q,IAAIgQ,IAC5Bza,KAAKgG,gBAPYwL,KACfxR,KAAKyF,SAAS5L,UAAU4Q,IAAIwe,IAC5BjpB,KAAKyF,SAAS5L,UAAUlC,OAAO8iB,GAAoBzK,IACnDzP,EAAasB,QAAQ7B,KAAKyF,SAAUsK,GAAa,GAIrB/P,KAAKyF,SAAUzF,KAAK0F,QAAQib,YAC5D,CAEA/a,UACE5F,KAAKspB,gBAEDtpB,KAAKwpB,WACPxpB,KAAKyF,SAAS5L,UAAUlC,OAAOqY,IAGjCxK,MAAMI,SACR,CAEA4jB,UACE,OAAOxpB,KAAKyF,SAAS5L,UAAUC,SAASkW,GAC1C,CAGAuZ,qBACOvpB,KAAK0F,QAAQwjB,WAIdlpB,KAAKopB,sBAAwBppB,KAAKqpB,0BAItCrpB,KAAKmhB,SAAW/jB,YAAW,KACzB4C,KAAK+Q,MAAM,GACV/Q,KAAK0F,QAAQob,QAClB,CAEA2I,eAAerqB,EAAOsqB,GACpB,OAAQtqB,EAAMqB,MACZ,IAAK,YACL,IAAK,WACHT,KAAKopB,qBAAuBM,EAC5B,MAGF,IAAK,UACL,IAAK,WACH1pB,KAAKqpB,wBAA0BK,EASnC,GAAIA,EAEF,YADA1pB,KAAKspB,gBAIP,MAAMva,EAAc3P,EAAMU,cACtBE,KAAKyF,WAAasJ,GAAe/O,KAAKyF,SAAS3L,SAASiV,IAI5D/O,KAAKupB,oBACP,CAEA9H,gBACElhB,EAAac,GAAGrB,KAAKyF,SAAUqjB,IAAiB1pB,GAASY,KAAKypB,eAAerqB,GAAO,KACpFmB,EAAac,GAAGrB,KAAKyF,SAAUsjB,IAAgB3pB,GAASY,KAAKypB,eAAerqB,GAAO,KACnFmB,EAAac,GAAGrB,KAAKyF,SAAUuQ,IAAe5W,GAASY,KAAKypB,eAAerqB,GAAO,KAClFmB,EAAac,GAAGrB,KAAKyF,SAAUujB,IAAgB5pB,GAASY,KAAKypB,eAAerqB,GAAO,IACrF,CAEAkqB,gBACE9a,aAAaxO,KAAKmhB,UAClBnhB,KAAKmhB,SAAW,IAClB,CAGA,sBAAO1lB,CAAgB+I,GACrB,OAAOxE,KAAK0I,MAAK,WACf,MAAMC,EAAOwgB,GAAMhjB,oBAAoBnG,KAAMwE,GAE7C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBmE,EAAKnE,GACd,MAAM,IAAIa,UAAU,oBAAoBb,MAG1CmE,EAAKnE,GAAQxE,KACf,CACF,GACF,E,OAOFgI,EAAqBmhB,IAMrBluB,EAAmBkuB,ICzMJ,CACb5gB,QACAO,SACA4D,YACA2D,YACA0C,YACA+F,SACA+B,aACAmJ,WACAU,aACA8C,OACA2B,SACAlI,W", "ignoreList": []}